#!/usr/bin/env python3
"""
Centralized Logging Configuration for GA Modbus Python Application

Provides standardized logging setup for debugging and troubleshooting runtime errors.
Logs are stored in the 'logger/' directory with rotation and proper formatting.
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


# Logger directory relative to project root
LOGGER_DIR = Path(__file__).parent.parent.parent / "logger"
LOGGER_DIR.mkdir(exist_ok=True)

# Log levels mapping
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}


def setup_logger(name: str, level: str = 'DEBUG', max_bytes: int = 10_000_000, backup_count: int = 5) -> logging.Logger:
    """
    Setup a logger with file rotation and console output.
    
    Args:
        name: Logger name (typically module name)
        level: Log level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        max_bytes: Maximum file size before rotation (default 10MB)
        backup_count: Number of backup files to keep (default 5)
    
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers if logger already configured
    if logger.handlers:
        return logger
    
    logger.setLevel(LOG_LEVELS.get(level.upper(), logging.DEBUG))
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with rotation
    log_filename = LOGGER_DIR / f"{name.replace('.', '_')}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        log_filename,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(LOG_LEVELS.get(level.upper(), logging.DEBUG))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Console handler (only for WARNING and above to avoid spam)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    console_formatter = logging.Formatter(
        fmt='%(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Log initial setup message
    logger.info(f"Logger '{name}' initialized - Log file: {log_filename}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get or create a logger for the given module.
    
    Args:
        name: Logger name (typically __name__ from calling module)
    
    Returns:
        Logger instance
    """
    return setup_logger(name)


def log_exception(logger: logging.Logger, message: str = "Exception occurred"):
    """
    Log an exception with full traceback.
    
    Args:
        logger: Logger instance
        message: Custom message to include with exception
    """
    logger.exception(message)


def log_function_entry(logger: logging.Logger, func_name: str, **kwargs):
    """
    Log function entry with parameters (DEBUG level).
    
    Args:
        logger: Logger instance
        func_name: Function name
        **kwargs: Function parameters to log
    """
    if kwargs:
        params = ', '.join(f"{k}={v}" for k, v in kwargs.items())
        logger.debug(f"Entering {func_name}({params})")
    else:
        logger.debug(f"Entering {func_name}()")


def log_function_exit(logger: logging.Logger, func_name: str, result=None):
    """
    Log function exit with optional return value (DEBUG level).
    
    Args:
        logger: Logger instance
        func_name: Function name
        result: Return value to log (optional)
    """
    if result is not None:
        logger.debug(f"Exiting {func_name}() -> {result}")
    else:
        logger.debug(f"Exiting {func_name}()")


def cleanup_old_logs(days_to_keep: int = 30):
    """
    Clean up log files older than specified days.
    
    Args:
        days_to_keep: Number of days to keep log files (default 30)
    """
    cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
    
    for log_file in LOGGER_DIR.glob("*.log*"):
        try:
            if log_file.stat().st_mtime < cutoff_time:
                log_file.unlink()
        except (OSError, PermissionError):
            pass


# Create main application logger
app_logger = setup_logger('ga_modbus_app', level='DEBUG')

# Log startup
app_logger.info("=== GA Modbus Python Application Logging Initialized ===")
app_logger.info(f"Log directory: {LOGGER_DIR}")
app_logger.info(f"Python logging level: DEBUG")