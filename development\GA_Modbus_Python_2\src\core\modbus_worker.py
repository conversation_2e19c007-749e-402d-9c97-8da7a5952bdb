"""
Modbus worker thread for non-blocking communication with battery management system.
"""

from datetime import datetime
from PyQt6.QtCore import QThread, pyqtSignal
from pymodbus.client import ModbusSerialClient
from pymodbus.exceptions import ModbusException

from ..utils.constants import REGISTER_MAP, DEFAULT_MODBUS_SETTINGS
from ..utils.formatters import scale_modbus_value


class ModbusWorker(QThread):
    """Worker thread for Modbus communication to prevent GUI blocking"""
    
    dataReceived = pyqtSignal(dict)
    statusChanged = pyqtSignal(str)
    errorOccurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.client = None
        self.running = False
        self.port = None
        self.baudrate = DEFAULT_MODBUS_SETTINGS['baudrate']
        self.parity = DEFAULT_MODBUS_SETTINGS['parity']
        self.slave_id = DEFAULT_MODBUS_SETTINGS['slave_id']
        self.interval = DEFAULT_MODBUS_SETTINGS['interval']
        self.register_map = REGISTER_MAP
    
    def configure(self, port: str, baudrate: int = None, parity: str = None, 
                  slave_id: int = None, interval: float = None):
        """Configure Modbus connection parameters"""
        self.port = port
        if baudrate is not None:
            self.baudrate = baudrate
        if parity is not None:
            self.parity = parity
        if slave_id is not None:
            self.slave_id = slave_id
        if interval is not None:
            self.interval = interval
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        self.running = True
        self.start()
    
    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.running = False
        if self.client and self.client.connected:
            self.client.close()
        self.wait()
    
    def run(self):
        """Main thread execution"""
        try:
            self.client = ModbusSerialClient(
                port=self.port,
                baudrate=self.baudrate,
                parity=self.parity,
                stopbits=DEFAULT_MODBUS_SETTINGS['stopbits'],
                bytesize=DEFAULT_MODBUS_SETTINGS['bytesize'],
                timeout=DEFAULT_MODBUS_SETTINGS['timeout'],
                retries=DEFAULT_MODBUS_SETTINGS['retries']
            )
            
            if not self.client.connect():
                self.errorOccurred.emit(f"Failed to connect to {self.port}")
                return
            
            self.statusChanged.emit("Connected")
            
            while self.running:
                try:
                    # Read registers (start at address 9, read 30 registers)
                    response = self.client.read_input_registers(
                        address=9,
                        count=30,
                        slave=self.slave_id
                    )
                    
                    if not response.isError():
                        # Parse register values
                        values = response.registers
                        mapped_data = self._process_register_data(values)
                        
                        # Add timestamp
                        mapped_data['timestamp'] = datetime.now()
                        
                        self.dataReceived.emit(mapped_data)
                        
                    else:
                        self.errorOccurred.emit(f"Modbus read error: {response}")
                        
                    self.msleep(int(self.interval * 1000))
                    
                except ModbusException as e:
                    self.errorOccurred.emit(f"Modbus exception: {e}")
                    self.msleep(5000)  # Wait 5 seconds before retry
                except Exception as e:
                    self.errorOccurred.emit(f"Unexpected error: {e}")
                    self.msleep(5000)
                    
        except Exception as e:
            self.errorOccurred.emit(f"Connection error: {e}")
        finally:
            if self.client and self.client.connected:
                self.client.close()
            self.statusChanged.emit("Disconnected")
    
    def _process_register_data(self, values):
        """Process raw register values into scaled engineering units"""
        mapped_data = {}
        
        for i, value in enumerate(values):
            register_address = 10 + i
            if register_address in self.register_map:
                param_name = self.register_map[register_address]
                scaled_value = scale_modbus_value(param_name, value)
                mapped_data[param_name] = scaled_value
        
        return mapped_data