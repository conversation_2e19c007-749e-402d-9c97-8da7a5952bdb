"""
Real-time plotting widget using PyQtGraph for battery data visualization.
"""

from datetime import datetime
from collections import deque
from typing import List

from PyQt6.QtWidgets import QWidget, QVBoxLayout
import pyqtgraph as pg

from ...utils.constants import PLOT_COLORS, DEFAULT_SETTINGS


class RealTimePlotWidget(QWidget):
    """Real-time plotting widget using PyQtGraph"""
    
    def __init__(self, title: str, y_label: str, line_colors: List[str] = None):
        super().__init__()
        self.title = title
        self.y_label = y_label
        self.line_colors = line_colors or PLOT_COLORS
        
        # Data storage (configurable max points)
        self.max_points = DEFAULT_SETTINGS['max_plot_points']
        self.time_data = deque(maxlen=self.max_points)
        self.plot_data = {}
        self.plot_lines = {}
        self.start_time = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the plot widget UI"""
        layout = QVBoxLayout()
        
        # Create plot widget
        self.plot_widget = pg.PlotWidget(title=self.title)
        self.plot_widget.setLabel('left', self.y_label)
        self.plot_widget.setLabel('bottom', 'Time (s)')
        self.plot_widget.showGrid(True, True)
        self.plot_widget.setBackground('w')
        
        # Enable auto-scaling
        self.plot_widget.enableAutoRange()
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
    
    def add_data_series(self, name: str, color: str = None):
        """Add a new data series to the plot"""
        if color is None:
            color = self.line_colors[len(self.plot_lines) % len(self.line_colors)]
        
        self.plot_data[name] = deque(maxlen=self.max_points)
        pen = pg.mkPen(color=color, width=2)
        self.plot_lines[name] = self.plot_widget.plot([], [], pen=pen, name=name)
    
    def update_data(self, name: str, value: float, timestamp: datetime):
        """Update data for a specific series"""
        if name not in self.plot_data:
            self.add_data_series(name)

        # Calculate time offset from first data point
        if not self.time_data:
            self.start_time = timestamp

        time_offset = (timestamp - self.start_time).total_seconds()

        # Only append time data if this is the first series or if time_data is shorter than expected
        if len(self.time_data) == 0 or len(self.time_data) <= len(self.plot_data[name]):
            self.time_data.append(time_offset)

        self.plot_data[name].append(value)

        # Ensure time_data and plot_data have the same length for this series
        time_data_for_series = list(self.time_data)[-len(self.plot_data[name]):]

        # Update the plot line
        self.plot_lines[name].setData(time_data_for_series, list(self.plot_data[name]))
    
    def clear_data(self):
        """Clear all plot data"""
        self.time_data.clear()
        for name in self.plot_data:
            self.plot_data[name].clear()
            self.plot_lines[name].setData([], [])
        self.start_time = None
    
    def set_max_points(self, max_points: int):
        """Set the maximum number of data points to display"""
        self.max_points = max_points
        self.time_data = deque(self.time_data, maxlen=max_points)
        for name in self.plot_data:
            self.plot_data[name] = deque(self.plot_data[name], maxlen=max_points)