"""
Real-time plotting widget using PyQtGraph
"""

from datetime import datetime
from collections import deque
from typing import List

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QApplication
from PyQt6.QtGui import QPalette
import pyqtgraph as pg

from ...utils.constants import MAX_DATA_POINTS, PLOT_COLORS


class RealTimePlotWidget(QWidget):
    """Real-time plotting widget using PyQtGraph"""
    
    def __init__(self, title: str, y_label: str, line_colors: List[str] = None):
        super().__init__()
        self.title = title
        self.y_label = y_label
        self.line_colors = line_colors or PLOT_COLORS
        
        # Data storage
        self.max_points = MAX_DATA_POINTS
        self.time_data = deque(maxlen=self.max_points)
        self.plot_data = {}
        self.plot_lines = {}
        self.current_theme = "light"
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the plot widget UI"""
        layout = QVBoxLayout()
        
        # Create plot widget
        self.plot_widget = pg.PlotWidget(title=self.title)
        self.plot_widget.setLabel('left', self.y_label)
        self.plot_widget.setLabel('bottom', 'Time (s)')
        self.plot_widget.showGrid(True, True)
        
        # Apply initial theme
        self.apply_theme(self.current_theme)
        
        # Enable auto-scaling
        self.plot_widget.enableAutoRange()
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
    
    def add_data_series(self, name: str, color: str = None):
        """Add a new data series to the plot"""
        if color is None:
            color = self.line_colors[len(self.plot_lines) % len(self.line_colors)]
        
        self.plot_data[name] = deque(maxlen=self.max_points)
        pen = pg.mkPen(color=color, width=2)
        self.plot_lines[name] = self.plot_widget.plot([], [], pen=pen, name=name)
    
    def update_data(self, name: str, value: float, timestamp: datetime):
        """Update data for a specific series"""
        if name not in self.plot_data:
            self.add_data_series(name)

        # Calculate time offset from first data point
        if not self.time_data:
            self.start_time = timestamp

        time_offset = (timestamp - self.start_time).total_seconds()

        # Only append time data if this is the first series or if time_data is shorter than expected
        if len(self.time_data) == 0 or len(self.time_data) <= len(self.plot_data[name]):
            self.time_data.append(time_offset)

        self.plot_data[name].append(value)

        # Ensure time_data and plot_data have the same length for this series
        time_data_for_series = list(self.time_data)[-len(self.plot_data[name]):]

        # Update the plot line
        self.plot_lines[name].setData(time_data_for_series, list(self.plot_data[name]))
    
    def apply_theme(self, theme: str):
        """Apply theme to the plot widget"""
        self.current_theme = theme
        
        if theme == "dark":
            # Dark theme
            self.plot_widget.setBackground('#2b2b2b')
            self.plot_widget.getAxis('left').setPen('#ffffff')
            self.plot_widget.getAxis('bottom').setPen('#ffffff')
            self.plot_widget.getAxis('left').setTextPen('#ffffff')
            self.plot_widget.getAxis('bottom').setTextPen('#ffffff')
            # Update grid color
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        elif theme == "light":
            # Light theme
            self.plot_widget.setBackground('#ffffff')
            self.plot_widget.getAxis('left').setPen('#000000')
            self.plot_widget.getAxis('bottom').setPen('#000000')
            self.plot_widget.getAxis('left').setTextPen('#000000')
            self.plot_widget.getAxis('bottom').setTextPen('#000000')
            # Update grid color
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        else:  # system
            # Use system theme colors
            palette = QApplication.palette()
            bg_color = palette.color(QPalette.ColorRole.Base)
            text_color = palette.color(QPalette.ColorRole.Text)
            
            self.plot_widget.setBackground(bg_color)
            self.plot_widget.getAxis('left').setPen(text_color)
            self.plot_widget.getAxis('bottom').setPen(text_color)
            self.plot_widget.getAxis('left').setTextPen(text_color)
            self.plot_widget.getAxis('bottom').setTextPen(text_color)
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)