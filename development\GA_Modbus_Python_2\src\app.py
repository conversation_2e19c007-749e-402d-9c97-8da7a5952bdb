#!/usr/bin/env python3
"""
GA Battery Management System - Real-time GUI Application
PyQt6 application for visualizing battery data via Modbus RTU communication
"""

import sys
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

# Import logging
from .utils.logger import get_logger, log_exception, log_function_entry, log_function_exit

# Optional imports for CSV viewer functionality
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False

try:
    import numpy as np
except ImportError:
    np = None

# PyQt6 imports
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QLabel, QComboBox, QSpinBox, QGroupBox, QGridLayout, 
    QStatusBar, QSplitter, QMessageBox, QFileDialog, QCheckBox, QTabWidget,
    QDouble<PERSON>pinBox, QFormLayout
)
from PyQt6.QtCore import QTimer, Qt, QSettings
from PyQt6.QtGui import QFont

# Serial imports
import serial.tools.list_ports

# Import modular components
from .communication.modbus_worker import ModbusWorker
from .gui.widgets.realtime_plot import RealTimePlotWidget
from .gui.widgets.data_table import DataTableWidget
from .gui.widgets.csv_log_table import CSVLogTableWidget
from .gui.widgets.changelog_viewer import ChangelogViewerWidget
from .gui.widgets.session_widget import SessionWidget
from .gui.csv_viewer.viewer_widget import CSVViewerWidget
from .gui.commands.commands_widget import CommandsWidget
from .themes.styles import apply_theme_to_widget
from .utils.constants import APP_VERSION, DEFAULT_LOG_DIR

# Import existing modules
try:
    from .ga_modbus_csv_writer import write_modbus_data_to_csv
except ImportError:
    def write_modbus_data_to_csv(data, filename):
        """Fallback CSV writer"""
        pass

# Import session manager
try:
    from .core.session_manager import SessionManager
except ImportError:
    SessionManager = None

# Import configuration manager
from .utils.config_manager import get_config_manager

# Load battery specifications
try:
    with open('battery_pack_spec_json.json', 'r') as f:
        BATTERY_SPEC = json.load(f)
except FileNotFoundError:
    BATTERY_SPEC = {}


class BMSMainWindow(QMainWindow):
    """Main window for the BMS monitoring application"""
    
    APP_VERSION = APP_VERSION
    
    def __init__(self):
        super().__init__()
        # Initialize logger
        self.logger = get_logger(__name__)
        self.logger.info("Initializing BMSMainWindow")
        
        self.settings = QSettings('GA_BMS', 'Monitor')
        
        # Initialize configuration manager
        self.config_manager = get_config_manager()
        self.current_theme = self.config_manager.get('gui.theme', 'light')
        
        # Initialize components
        self.modbus_worker = None
        try:
            self.session_manager = SessionManager(self.settings) if SessionManager else None
            self.logger.info("SessionManager initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize SessionManager: {e}")
            self.session_manager = None
        
        self.csv_timer = QTimer()
        self.csv_filename = None
        self.logging_enabled = False
        
        # Initialize UI
        try:
            self.setup_ui()
            self.setup_connections()
            self.load_settings()
            self.logger.info("UI initialization completed")
        except Exception as e:
            self.logger.error(f"UI initialization failed: {e}")
            log_exception(self.logger, "UI initialization error")
            raise
        
        # Apply initial theme
        self.apply_theme(self.current_theme)
        self.logger.info(f"Applied theme: {self.current_theme}")
        
        # Initialize session display
        self.update_session_display()
        
        # Setup timer for periodic session display updates
        self.session_update_timer = QTimer()
        self.session_update_timer.timeout.connect(self.update_session_display)
        self.session_update_timer.start(5000)  # Update every 5 seconds
        
    def setup_ui(self):
        """Setup the main UI"""
        self.setWindowTitle(f"GA BMS Monitor v{self.APP_VERSION}")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget and tabs
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Setup tabs - Sessions first
        self.setup_sessions_tab()
        self.setup_monitoring_tab()
        self.setup_csv_log_tab()
        self.setup_csv_viewer_tab()
        self.setup_commands_tab()
        self.setup_changelog_tab()
        self.setup_settings_tab()
        
        # Setup status bar
        self.setup_status_bar()
        
    def setup_monitoring_tab(self):
        """Setup the main monitoring tab"""
        monitoring_widget = QWidget()
        layout = QHBoxLayout(monitoring_widget)
        
        # Left side - Controls and data table
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Connection controls
        connection_group = self.create_connection_controls()
        left_layout.addWidget(connection_group)
        
        # Data table
        self.data_table = DataTableWidget()
        left_layout.addWidget(self.data_table)
        
        # Right side - Plots
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Cell voltage plot
        self.cell_voltage_plot = RealTimePlotWidget("Cell Voltages", "Voltage (V)")
        right_layout.addWidget(self.cell_voltage_plot)
        
        # Pack voltage and current plot
        self.pack_plot = RealTimePlotWidget("Pack Voltage & Current", "Value")
        right_layout.addWidget(self.pack_plot)
        
        # Add to splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        layout.addWidget(splitter)
        
        self.tabs.addTab(monitoring_widget, "Real-Time Monitoring")
        
    def setup_sessions_tab(self):
        """Setup the sessions tab"""
        self.session_widget = SessionWidget(self.session_manager)
        self.tabs.addTab(self.session_widget, "Sessions")
        
    def setup_csv_log_tab(self):
        """Setup the CSV logging tab"""
        csv_widget = QWidget()
        layout = QVBoxLayout(csv_widget)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.logging_checkbox = QCheckBox("Enable CSV Logging")
        controls_layout.addWidget(self.logging_checkbox)
        
        self.clear_log_button = QPushButton("Clear Log")
        controls_layout.addWidget(self.clear_log_button)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # CSV log table
        self.csv_log_table = CSVLogTableWidget()
        layout.addWidget(self.csv_log_table)
        
        self.tabs.addTab(csv_widget, "CSV Log")
        
    def setup_csv_viewer_tab(self):
        """Setup the CSV viewer tab"""
        self.csv_viewer_widget = CSVViewerWidget()
        self.tabs.addTab(self.csv_viewer_widget, "CSV Viewer")
        
    def setup_commands_tab(self):
        """Setup the commands tab"""
        self.commands_widget = CommandsWidget()
        self.tabs.addTab(self.commands_widget, "Commands")
        
    def setup_changelog_tab(self):
        """Setup the changelog tab"""
        self.changelog_widget = ChangelogViewerWidget()
        self.tabs.addTab(self.changelog_widget, "Changelog")
        
    def setup_settings_tab(self):
        """Setup the settings tab"""
        settings_widget = QWidget()
        layout = QFormLayout(settings_widget)
        
        # Theme selection
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["light", "dark", "system"])
        self.theme_combo.setCurrentText(self.current_theme)
        layout.addRow("Theme:", self.theme_combo)
        
        # Connection settings (see Connection Control panel for main settings)
        layout.addRow("Connection Settings:", QLabel("Use Connection Control panel above"))
        
        # Additional settings
        self.update_interval_spin = QDoubleSpinBox()
        self.update_interval_spin.setRange(0.1, 10.0)
        self.update_interval_spin.setValue(1.0)
        self.update_interval_spin.setSuffix(" s")
        layout.addRow("Plot Update Interval:", self.update_interval_spin)
        
        self.tabs.addTab(settings_widget, "Settings")
        
    def create_connection_controls(self):
        """Create connection control group"""
        group = QGroupBox("Connection Control")
        layout = QGridLayout(group)
        
        # Port selection
        layout.addWidget(QLabel("Serial Port:"), 0, 0)
        self.port_combo = QComboBox()
        self.refresh_ports()
        layout.addWidget(self.port_combo, 0, 1)
        
        # Refresh ports button
        self.refresh_button = QPushButton("Refresh")
        layout.addWidget(self.refresh_button, 0, 2)
        
        # Baud rate
        layout.addWidget(QLabel("Baud Rate:"), 0, 3)
        self.baudrate_spin = QSpinBox()
        self.baudrate_spin.setRange(1200, 115200)
        self.baudrate_spin.setValue(9600)
        layout.addWidget(self.baudrate_spin, 0, 4)
        
        # Slave ID
        layout.addWidget(QLabel("Slave ID:"), 0, 5)
        self.slave_id_spin = QSpinBox()
        self.slave_id_spin.setRange(1, 247)
        self.slave_id_spin.setValue(1)
        layout.addWidget(self.slave_id_spin, 0, 6)
        
        # Control buttons
        self.start_btn = QPushButton("Start Monitoring")
        layout.addWidget(self.start_btn, 1, 0, 1, 2)
        
        self.stop_btn = QPushButton("Stop Monitoring")
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn, 1, 2, 1, 2)
        
        # CSV logging checkbox
        self.logging_checkbox = QCheckBox("Enable CSV Logging")
        layout.addWidget(self.logging_checkbox, 1, 4, 1, 3)
        
        # Status label
        self.status_label = QLabel("Disconnected")
        layout.addWidget(self.status_label, 2, 0, 1, 7)
        
        return group
        
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
    def setup_connections(self):
        """Setup signal connections"""
        self.start_btn.clicked.connect(self.start_monitoring)
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.refresh_button.clicked.connect(self.refresh_ports)
        self.theme_combo.currentTextChanged.connect(self.apply_theme)
        self.logging_checkbox.toggled.connect(self.toggle_csv_logging)
        self.clear_log_button.clicked.connect(self.csv_log_table.clear_data)
        
        # Connect session manager signals if available
        if self.session_manager:
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
        
    def refresh_ports(self):
        """Refresh available serial ports"""
        self.port_combo.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_combo.addItem(f"{port.device} - {port.description}")
            
    def start_monitoring(self):
        """Start Modbus monitoring"""
        log_function_entry(self.logger, "start_monitoring")
        
        port_text = self.port_combo.currentText()
        if not port_text:
            self.logger.warning("No serial port selected")
            QMessageBox.warning(self, "Warning", "Please select a serial port")
            return
            
        port = port_text.split(' - ')[0]
        self.logger.info(f"Starting monitoring on port: {port}")
        
        # Save connection parameters to config.toml
        self.config_manager.update_last_connection(
            port=port,
            baudrate=self.baudrate_spin.value(),
            slave_id=self.slave_id_spin.value(),
            save=True
        )
        
        try:
            # Create and configure worker
            self.modbus_worker = ModbusWorker(self.session_manager)
            self.modbus_worker.configure(
                port=port,
                baudrate=self.baudrate_spin.value(),
                slave_id=self.slave_id_spin.value(),
                interval=1.0  # Fixed interval for now
            )
            
            # Connect signals
            self.modbus_worker.dataReceived.connect(self.handle_data_received)
            self.modbus_worker.statusChanged.connect(self.handle_status_changed)
            self.modbus_worker.errorOccurred.connect(self.handle_error)
            
            # Start a monitoring session if session manager is available
            if self.session_manager:
                connection_params = {
                    'port': port,
                    'baudrate': self.baudrate_spin.value(),
                    'slave_id': self.slave_id_spin.value(),
                    'interval': 1.0
                }
                session_id = self.session_manager.start_manual_session(connection_params)
                self.logger.info(f"Started monitoring session: {session_id}")
                
                # Update session widget
                self.update_session_display()
            
            # Setup CSV logging if enabled (after session is created)
            if self.logging_checkbox.isChecked():
                self.setup_csv_logging()
            
            # Start monitoring
            self.modbus_worker.start_monitoring()
            
            # Update UI
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.port_combo.setEnabled(False)
            self.baudrate_spin.setEnabled(False)
            self.slave_id_spin.setEnabled(False)
            
            self.status_label.setText("Connected")
            self.logger.info("Monitoring started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
            log_exception(self.logger, "Error starting monitoring")
            QMessageBox.critical(self, "Error", f"Failed to start monitoring: {e}")
        
        log_function_exit(self.logger, "start_monitoring")
        
    def stop_monitoring(self):
        """Stop Modbus monitoring"""
        if self.modbus_worker:
            self.modbus_worker.stop_monitoring()
            self.modbus_worker = None
            
        # End the current session if session manager is available
        if self.session_manager and self.session_manager.current_session:
            self.session_manager.end_session()
            self.logger.info("Ended monitoring session")
            
            # Update session widget
            self.update_session_display()
            
        # Update UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.port_combo.setEnabled(True)
        self.baudrate_spin.setEnabled(True)
        self.slave_id_spin.setEnabled(True)
        
        self.status_label.setText("Disconnected")
        
    def handle_data_received(self, data: Dict):
        """Handle received Modbus data"""
        # Update session with new data if session manager is available
        if self.session_manager and self.session_manager.current_session:
            # Transform data format for session manager
            session_data = self.transform_data_for_session(data)
            self.session_manager.update_session_data(session_data)
            
        # Update data table
        self.data_table.update_data(data)
        
        # Update plots
        timestamp = data.get('timestamp', datetime.now())
        
        # Cell voltages
        for i in range(1, 9):
            param = f"afe_cell_volt{i}"
            if param in data:
                self.cell_voltage_plot.update_data(f"Cell {i}", data[param], timestamp)
                
        # Pack voltage and current
        if 'afe_pack_volt' in data:
            self.pack_plot.update_data("Pack Voltage", data['afe_pack_volt'], timestamp)
        if 'afe_current' in data:
            self.pack_plot.update_data("Current", data['afe_current'], timestamp)
            
        # CSV logging
        if self.logging_enabled:
            self.csv_log_table.update_data(data)
            self.write_csv_data(data)
            
    def handle_status_changed(self, status: str):
        """Handle status changes"""
        self.status_label.setText(status)
        self.status_bar.showMessage(f"Connection: {status}")
        
    def handle_error(self, error: str):
        """Handle errors"""
        self.logger.error(f"Modbus error: {error}")
        self.status_bar.showMessage(f"Error: {error}")
        QMessageBox.critical(self, "Connection Error", error)
        
    def toggle_csv_logging(self, enabled: bool):
        """Toggle CSV logging"""
        self.logging_enabled = enabled
        
        # Save setting immediately to both config.toml and QSettings
        self.config_manager.set('gui.csv_logging_enabled', enabled, save=True)
        self.settings.setValue('csv_logging_enabled', enabled)
        
        if enabled:
            self.setup_csv_logging()
        else:
            self.csv_filename = None
            
    def setup_csv_logging(self):
        """Setup CSV logging filename"""
        os.makedirs(DEFAULT_LOG_DIR, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_filename = os.path.join(DEFAULT_LOG_DIR, f"battery_data_{timestamp}.csv")
        
        # Associate CSV file with current session
        if self.session_manager and self.session_manager.current_session:
            filename = os.path.basename(self.csv_filename)
            self.session_manager.associate_csv_file(filename)
        
    def write_csv_data(self, data: Dict):
        """Write data to CSV file"""
        if self.csv_filename:
            write_modbus_data_to_csv(data, self.csv_filename)
            
    def transform_data_for_session(self, data: Dict) -> Dict:
        """Transform Modbus data format for session manager"""
        session_data = {}
        
        # Extract cell voltages
        cell_voltages = []
        for i in range(1, 9):  # Assuming 8 cells
            cell_key = f"afe_cell_volt{i}"
            if cell_key in data:
                cell_voltages.append(data[cell_key])
        
        if cell_voltages:
            session_data['cell_voltages'] = cell_voltages
            
        # Extract pack voltage
        if 'afe_pack_volt' in data:
            session_data['pack_voltage'] = data['afe_pack_volt']
            
        # Extract current
        if 'afe_current' in data:
            session_data['current'] = data['afe_current']
            
        # Include timestamp
        if 'timestamp' in data:
            session_data['timestamp'] = data['timestamp']
            
        return session_data
            
    def update_session_display(self):
        """Update session display in the session widget"""
        if self.session_manager:
            status = self.session_manager.get_current_session_status()
            if status['active']:
                session_info = f"Active Session: {status['session_id'][:8]}... | Type: {status['trigger_type']} | Duration: {status['duration']}s | Records: {status['record_count']}"
            else:
                session_info = "Status: No active session"
            
            # Update session widget display
            if hasattr(self, 'session_widget'):
                self.session_widget.update_current_session(session_info)
                
    def on_session_started(self, session_id: str, trigger_type: str):
        """Handle session started signal"""
        self.logger.info(f"Session started: {session_id} ({trigger_type})")
        self.update_session_display()
        # Refresh session history to show the new session
        if hasattr(self, 'session_widget'):
            self.session_widget.refresh_session_history()
            
    def on_session_ended(self, session_id: str, session_summary: Dict):
        """Handle session ended signal"""
        self.logger.info(f"Session ended: {session_id}")
        self.update_session_display()
        # Refresh session history to show the updated session
        if hasattr(self, 'session_widget'):
            self.session_widget.refresh_session_history()
            
    def apply_theme(self, theme: str):
        """Apply theme to the application"""
        self.current_theme = theme
        
        # Save theme to config.toml
        self.config_manager.set('gui.theme', theme, save=True)
        
        apply_theme_to_widget(self, theme)
        
        # Apply to plots
        if hasattr(self, 'cell_voltage_plot'):
            self.cell_voltage_plot.apply_theme(theme)
        if hasattr(self, 'pack_plot'):
            self.pack_plot.apply_theme(theme)
            
    def load_settings(self):
        """Load application settings from config.toml and QSettings"""
        self.theme_combo.setCurrentText(self.current_theme)
        
        # Load CSV logging setting from config.toml - enabled by default
        csv_logging_enabled = self.config_manager.get('gui.csv_logging_enabled', True)
        self.logging_checkbox.setChecked(csv_logging_enabled)
        self.logging_enabled = csv_logging_enabled
        
        # Load last connection settings from config.toml
        last_connection = self.config_manager.get_last_connection()
        
        # Set last serial port if available
        if last_connection['port']:
            # Find and select the last used port
            for i in range(self.port_combo.count()):
                port_text = self.port_combo.itemText(i)
                if port_text.startswith(last_connection['port']):
                    self.port_combo.setCurrentIndex(i)
                    break
        
        # Set last baudrate and slave ID
        self.baudrate_spin.setValue(last_connection['baudrate'])
        self.slave_id_spin.setValue(last_connection['slave_id'])
        
        # Load update interval
        update_interval = self.config_manager.get('gui.update_interval', 1.0)
        self.update_interval_spin.setValue(update_interval)
        
    def save_settings(self):
        """Save application settings to config.toml"""
        # Save GUI settings to config.toml
        gui_config = {
            'theme': self.current_theme,
            'csv_logging_enabled': self.logging_enabled,
            'update_interval': self.update_interval_spin.value()
        }
        self.config_manager.set_gui_config(gui_config, save=True)
        
        # Also save to QSettings for backward compatibility
        self.settings.setValue('theme', self.current_theme)
        self.settings.setValue('csv_logging_enabled', self.logging_enabled)
        
    def closeEvent(self, event):
        """Handle application close"""
        self.logger.info("Application closing")
        try:
            self.stop_monitoring()
            self.save_settings()
            self.logger.info("Application closed successfully")
        except Exception as e:
            self.logger.error(f"Error during application close: {e}")
            log_exception(self.logger, "Error during application close")
        event.accept()


def main():
    """Main application entry point"""
    # Initialize main app logger
    main_logger = get_logger('ga_modbus_app.main')
    main_logger.info("=== Starting GA BMS Monitor Application ===")
    
    try:
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("GA BMS Monitor")
        app.setApplicationVersion(APP_VERSION)
        main_logger.info(f"Application version: {APP_VERSION}")
        
        # Create and show main window
        window = BMSMainWindow()
        window.show()
        main_logger.info("Main window created and shown")
        
        # Start event loop
        main_logger.info("Starting Qt event loop")
        sys.exit(app.exec())
        
    except Exception as e:
        main_logger.critical(f"Fatal error in main(): {e}")
        log_exception(main_logger, "Critical application error")
        sys.exit(1)


if __name__ == "__main__":
    main()