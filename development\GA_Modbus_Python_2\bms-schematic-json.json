{"project": {"name": "MSC-WPA-0239", "company": "Custom Power", "confidential": true, "revision": "B", "date": "03/13/23", "total_sheets": 7, "overall_description": "Battery Management System with cell balancing, fuel gauging, microcontroller control, and high-voltage MOSFET switching for multi-cell battery packs. System includes comprehensive monitoring, protection, and communication interfaces."}, "sheets": {"sheet_1": {"title": "Cover/Block Diagram", "description": "Title page and system block diagram showing overall BMS architecture with main functional blocks and interconnections", "main_components": [], "connections": "Not applicable - cover sheet", "connectors": []}, "sheet_2": {"title": "System Overview and Main Connections", "description": "Block diagram showing the overall BMS architecture including battery connections, fuel gauge interface, cell balancing sections, microcontroller, and power control blocks with main system connectors", "main_components": [{"id": "BATT_POS", "part": "Connection Block", "type": "Battery Interface", "function": "Main battery positive connection and monitoring", "pinouts": []}, {"id": "Fuel Gauge Block", "part": "BQ34z110", "type": "Fuel Gauge IC", "function": "Battery state monitoring and coulomb counting", "pinouts": []}, {"id": "Microcontroller Block", "part": "MAX32630", "type": "System Controller", "function": "Main BMS control and communication", "pinouts": []}], "connections": "Battery positive feeds to multiple subsystems including cell balancing blocks, fuel gauge, and power control. I2C communication bus connects microcontroller to fuel gauge. SPI and alert signals interconnect various monitoring blocks. Pack connections route through protection circuitry", "connectors": [{"id": "PACK+", "part": "PCB Edge Connector", "type": "Power Output", "function": "Protected battery pack positive output", "pinouts": [{"pin_number": "1", "pin_name": "PACK+", "connection": "Through protection FETs to BATT_POS", "function": "Main power output"}]}, {"id": "PACK-", "part": "PCB Edge Connector", "type": "Power Output", "function": "Battery pack negative output", "pinouts": [{"pin_number": "1", "pin_name": "PACK-", "connection": "BATT_NEG", "function": "Main power return"}]}]}, "sheet_3": {"title": "Top Cell Balancing", "description": "Cell balancing circuit for the top three battery cells using P-channel MOSFETs and resistive discharge paths", "main_components": [{"id": "Q15", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for top cell", "pinouts": [{"pin_name": "Gate", "connection": "R37 (10Ω)", "function": "Control input for balancing activation"}, {"pin_name": "Source", "connection": "BATT_POS", "function": "Connected to battery positive"}, {"pin_name": "<PERSON><PERSON>", "connection": "R38 (10Ω)", "function": "Discharge path"}]}, {"id": "Q16", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for middle cell", "pinouts": [{"pin_name": "Gate", "connection": "R37 (10Ω)", "function": "Control input"}, {"pin_name": "Source", "connection": "VC4", "function": "Cell voltage connection"}, {"pin_name": "<PERSON><PERSON>", "connection": "R39 (10Ω)", "function": "Discharge path"}]}, {"id": "Q17", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for lower cell", "pinouts": [{"pin_name": "Gate", "connection": "R40 (10Ω)", "function": "Control input"}, {"pin_name": "Source", "connection": "VC3", "function": "Cell voltage connection"}, {"pin_name": "<PERSON><PERSON>", "connection": "R41 (10Ω)", "function": "Discharge path"}]}, {"id": "TP5", "part": "Test Point", "type": "Test Point", "function": "Test access for cell voltage", "pinouts": []}], "connections": "Each MOSFET source connects to respective cell voltage points (BATT_POS, VC4, VC3). Gates controlled through 10Ω resistors. Drains connect to discharge resistors creating balancing current paths. Capacitors C11-C14 provide filtering. Test points TP5-TP8 provide voltage monitoring access", "connectors": [{"id": "Cell Connector", "part": "Multi-pin Header", "type": "Cell Voltage Interface", "function": "Connection to battery cell taps", "pinouts": [{"pin_number": "1", "pin_name": "BATT_POS", "connection": "Top of battery stack", "function": "Highest voltage cell connection"}, {"pin_number": "2", "pin_name": "VC5", "connection": "Cell 5 positive", "function": "Cell tap"}, {"pin_number": "3", "pin_name": "VC4", "connection": "Cell 4 positive", "function": "Cell tap"}, {"pin_number": "4", "pin_name": "VC3", "connection": "Cell 3 positive", "function": "Cell tap"}]}]}, "sheet_4": {"title": "Bottom Cell Balancing", "description": "Cell balancing circuit for the bottom battery cells with similar P-MOSFET discharge architecture", "main_components": [{"id": "Q11", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for cell 3", "pinouts": [{"pin_name": "Gate", "connection": "R19 (10Ω)", "function": "Balancing control"}, {"pin_name": "Source", "connection": "VC3", "function": "Cell 3 voltage"}, {"pin_name": "<PERSON><PERSON>", "connection": "R20 (10Ω)", "function": "Discharge path"}]}, {"id": "Q12", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for cell 2", "pinouts": [{"pin_name": "Gate", "connection": "R21 (10Ω)", "function": "Balancing control"}, {"pin_name": "Source", "connection": "VC2", "function": "Cell 2 voltage"}, {"pin_name": "<PERSON><PERSON>", "connection": "R22 (10Ω)", "function": "Discharge path"}]}, {"id": "Q13", "part": "BSS84", "type": "P-MOSFET", "function": "Cell balancing switch for cell 1", "pinouts": [{"pin_name": "Gate", "connection": "R17 (10Ω)", "function": "Balancing control"}, {"pin_name": "Source", "connection": "VC1", "function": "Cell 1 voltage"}, {"pin_name": "<PERSON><PERSON>", "connection": "R18 (10Ω)", "function": "Discharge path"}]}], "connections": "MOSFETs Q11-Q14 connect between cell voltages VC0-VC3 with gate control through 10Ω resistors and discharge paths through additional 10Ω resistors. BATT_NEG connection provides ground reference. Capacitors provide filtering on each cell connection", "connectors": [{"id": "Cell Connector Lower", "part": "Multi-pin Header", "type": "Cell Voltage Interface", "function": "Connection to lower battery cell taps", "pinouts": [{"pin_number": "1", "pin_name": "VC3", "connection": "Cell 3 positive", "function": "Cell tap"}, {"pin_number": "2", "pin_name": "VC2", "connection": "Cell 2 positive", "function": "Cell tap"}, {"pin_number": "3", "pin_name": "VC1", "connection": "Cell 1 positive", "function": "Cell tap"}, {"pin_number": "4", "pin_name": "VC0/BATT_NEG", "connection": "Battery negative", "function": "Ground reference"}]}]}, "sheet_5": {"title": "Analog Front End", "description": "AFE circuit for battery monitoring with voltage sensing, current sensing, and communication interfaces including protection FET control", "main_components": [{"id": "U16", "part": "MMBT3906-7-F", "type": "PNP Transistor", "function": "Level shifting/control", "pinouts": [{"pin_name": "Base", "connection": "Control signal", "function": "Control input"}, {"pin_name": "Emitter", "connection": "Supply voltage", "function": "Current source"}, {"pin_name": "Collector", "connection": "Output", "function": "Switched output"}]}, {"id": "AFE IC", "part": "BQ76920", "type": "Battery Monitor AFE", "function": "Multi-cell battery monitor and protector", "pinouts": [{"pin_name": "VC0-VC5", "connection": "Battery cell voltages", "function": "Cell voltage monitoring inputs"}, {"pin_name": "SRP/SRN", "connection": "Current sense resistor", "function": "Current monitoring"}, {"pin_name": "ALERT", "connection": "AFE_ALERT to MCU", "function": "Alert signal output"}, {"pin_name": "SCL/SDA", "connection": "I2C bus", "function": "Communication interface"}, {"pin_name": "CHG/DSG", "connection": "FET control outputs", "function": "Charge/discharge FET control"}]}], "connections": "Cell voltages VC0-VC5 connect to AFE monitoring inputs through RC filters. Current sensing through SRP/SRN pins across sense resistor. I2C communication (SCL/SDA) to microcontroller. ALERT signal for fault conditions. CHG/DSG outputs control protection FETs through level shifting circuits", "connectors": [{"id": "AFE_I2C", "part": "4-pin Connector", "type": "I2C Interface", "function": "AFE communication interface", "pinouts": [{"pin_number": "1", "pin_name": "VCC", "connection": "3.3V", "function": "Power supply"}, {"pin_number": "2", "pin_name": "SCL", "connection": "I2C clock", "function": "Clock signal"}, {"pin_number": "3", "pin_name": "SDA", "connection": "I2C data", "function": "Data signal"}, {"pin_number": "4", "pin_name": "GND", "connection": "Ground", "function": "Reference ground"}]}]}, "sheet_6": {"title": "Fuel Gauge", "description": "Battery fuel gauge circuit with dedicated monitoring IC, voltage regulation, and temperature sensing interfaces", "main_components": [{"id": "U11", "part": "TPS62815ILSR", "type": "Buck <PERSON>", "function": "Voltage regulator for fuel gauge power supply", "pinouts": [{"pin_name": "VIN", "connection": "Input voltage", "function": "Power input"}, {"pin_name": "VOUT", "connection": "3.3V rail", "function": "Regulated 3.3V output"}, {"pin_name": "EN", "connection": "Enable signal", "function": "Regulator enable"}, {"pin_name": "PGOOD", "connection": "Power good indicator", "function": "Output status"}]}, {"id": "U2", "part": "LZ29z1DXU-J-3-NOPB", "type": "Voltage Reference", "function": "Precision 3.3V voltage reference", "pinouts": [{"pin_name": "IN", "connection": "Supply voltage", "function": "Input power"}, {"pin_name": "OUT", "connection": "3.3V reference", "function": "Reference output"}, {"pin_name": "GND", "connection": "Ground", "function": "Reference ground"}]}, {"id": "BQ Fuel Gauge", "part": "BQ34110PWR", "type": "Fuel Gauge IC", "function": "Battery capacity monitoring and state-of-charge calculation", "pinouts": [{"pin_name": "BAT", "connection": "BATT_POS through filter", "function": "Battery voltage sense"}, {"pin_name": "SRP/SRN", "connection": "Current sense resistor", "function": "Current measurement differential inputs"}, {"pin_name": "SCL/SDA", "connection": "Fuel_SCL/Fuel_SDA", "function": "I2C Communication"}, {"pin_name": "TS1/TS2", "connection": "Temperature sensors", "function": "Temperature monitoring inputs"}, {"pin_name": "ALERT", "connection": "Fuel_Alert signal", "function": "Alert output"}]}], "connections": "Fuel gauge connects to battery positive through RC filter. Current measurement via differential sensing across precision resistor. I2C interface (Fuel_SCL/Fuel_SDA) for MCU communication. Dual temperature sensing inputs for pack temperature monitoring. Alert output for fault conditions", "connectors": [{"id": "TP_BATT+", "part": "Test Point", "type": "Test Point", "function": "Battery positive voltage measurement", "pinouts": [{"pin_number": "1", "pin_name": "BATT+", "connection": "Battery positive", "function": "Test access"}]}, {"id": "TP_FT_HI", "part": "Test Point", "type": "Test Point", "function": "Fuel gauge temperature high test point", "pinouts": [{"pin_number": "1", "pin_name": "Fuel_Temp_HI", "connection": "Temperature sensor high", "function": "Temperature monitoring"}]}, {"id": "TP_FT_LO", "part": "Test Point", "type": "Test Point", "function": "Fuel gauge temperature low test point", "pinouts": [{"pin_number": "1", "pin_name": "Fuel_Temp_LO", "connection": "Temperature sensor low", "function": "Temperature monitoring"}]}, {"id": "Temp Sensor Connector", "part": "2-pin Connector", "type": "Temperature Sensor Interface", "function": "External temperature sensor connection", "pinouts": [{"pin_number": "1", "pin_name": "Fuel_Temp_HI", "connection": "TS1 input", "function": "Temperature sensor positive"}, {"pin_number": "2", "pin_name": "Fuel_Temp_LO", "connection": "TS2 input", "function": "Temperature sensor negative"}]}]}, "sheet_7": {"title": "High Voltage MOSFET Driver and Microcontroller", "description": "Power MOSFET control circuit for battery pack charge/discharge switching and main system microcontroller with programming interfaces", "main_components": [{"id": "Q9-Q20", "part": "CSD18532Q5B", "type": "N-MOSFET", "function": "High current switching MOSFETs for battery pack control", "pinouts": [{"pin_name": "Gate", "connection": "Driver outputs through resistors", "function": "Switching control"}, {"pin_name": "<PERSON><PERSON>", "connection": "Battery/Pack connection", "function": "Main current path"}, {"pin_name": "Source", "connection": "Common source connection", "function": "Current return path"}]}, {"id": "MAX32630", "part": "MAX32630FTHR", "type": "ARM Cortex-M4 Microcontroller", "function": "Main system controller for BMS operation", "pinouts": [{"pin_name": "P0.14/P0.15", "connection": "I2C0_SCL/SDA", "function": "I2C communication with AFE"}, {"pin_name": "P0.16/P0.17", "connection": "I2C1_SCL/SDA", "function": "I2C communication with Fuel Gauge"}, {"pin_name": "P0.0-P0.13", "connection": "GPIO outputs", "function": "Cell balancing control"}, {"pin_name": "SWDIO/SWDCLK", "connection": "Debug header", "function": "Programming/debug interface"}, {"pin_name": "AIN0-AIN3", "connection": "Analog inputs", "function": "Voltage/temperature monitoring"}]}], "connections": "Multiple power MOSFETs (Q9-Q20) arranged in parallel groups for charge and discharge paths. Gates driven through individual resistors from driver outputs. Source-drain paths handle main battery current. Microcontroller provides system control via I2C buses, GPIO for balancing, and analog inputs for monitoring", "connectors": [{"id": "PACK Connector", "part": "High Current Connector", "type": "Power Output", "function": "Main pack output connection", "pinouts": [{"pin_number": "1", "pin_name": "PACK+", "connection": "Through MOSFETs to BATT+", "function": "Protected positive output"}, {"pin_number": "2", "pin_name": "PACK-", "connection": "BATT_NEG", "function": "Negative output"}]}, {"id": "S<PERSON> Header", "part": "Programming Header", "type": "Debug/Programming Interface", "function": "Microcontroller programming and debugging", "pinouts": [{"pin_number": "1", "pin_name": "VCC", "connection": "3.3V supply", "function": "Target power"}, {"pin_number": "2", "pin_name": "SWDIO", "connection": "MCU SWDIO pin", "function": "Serial Wire Debug data"}, {"pin_number": "3", "pin_name": "SWDCLK", "connection": "MCU SWDCLK pin", "function": "Serial Wire Debug clock"}, {"pin_number": "4", "pin_name": "GND", "connection": "Ground", "function": "Reference ground"}, {"pin_number": "5", "pin_name": "RESET", "connection": "MCU reset", "function": "System reset"}]}, {"id": "I2C_Motor", "part": "4-pin Connector", "type": "Motor Control Interface", "function": "External motor control communication", "pinouts": [{"pin_number": "1", "pin_name": "VCC", "connection": "3.3V", "function": "Power supply"}, {"pin_number": "2", "pin_name": "SCL_Motor", "connection": "I2C clock", "function": "Clock signal"}, {"pin_number": "3", "pin_name": "SDA_Motor", "connection": "I2C data", "function": "Data signal"}, {"pin_number": "4", "pin_name": "GND", "connection": "Ground", "function": "Reference"}]}, {"id": "UART Connector", "part": "3-pin Header", "type": "Serial Interface", "function": "UART communication port", "pinouts": [{"pin_number": "1", "pin_name": "TX", "connection": "MCU UART TX", "function": "Transmit data"}, {"pin_number": "2", "pin_name": "RX", "connection": "MCU UART RX", "function": "Receive data"}, {"pin_number": "3", "pin_name": "GND", "connection": "Ground", "function": "Reference"}]}, {"id": "CAN Connector", "part": "3-pin Connector", "type": "CAN Bus Interface", "function": "CAN communication interface", "pinouts": [{"pin_number": "1", "pin_name": "CAN_H", "connection": "CAN transceiver high", "function": "CAN high signal"}, {"pin_number": "2", "pin_name": "CAN_L", "connection": "CAN transceiver low", "function": "CAN low signal"}, {"pin_number": "3", "pin_name": "GND", "connection": "Ground", "function": "Reference"}]}]}}}