"""
CSV log table widget for displaying real-time logging data
"""

from datetime import datetime
from typing import Dict
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView


class CSVLogTableWidget(QWidget):
    """Widget for displaying CSV logging data in real-time"""
    
    def __init__(self, max_rows: int = 100):
        super().__init__()
        self.max_rows = max_rows
        self.log_data = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the CSV log table widget UI"""
        layout = QVBoxLayout()
        
        # Create table
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSortingEnabled(True)
        
        # Headers will be set when first data arrives
        self.headers_set = False
        
        layout.addWidget(self.table)
        self.setLayout(layout)
    
    def update_data(self, data: Dict[str, float]):
        """Add new row of data to the CSV log table"""
        # Convert data to row format
        timestamp = data.get('timestamp', datetime.now())
        
        # Create row data with timestamp first
        row_data = {'Timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S')}
        
        # Add all other parameters
        for param, value in data.items():
            if param == 'timestamp':
                continue
                
            # Format value based on parameter type
            if 'volt' in param:
                formatted_value = f"{value:.3f}"
            elif 'current' in param:
                formatted_value = f"{value:.3f}"
            elif 'temp' in param:
                formatted_value = f"{value:.1f}"
            elif 'charge' in param:
                formatted_value = f"{value:.1f}"
            elif 'capacity' in param:
                formatted_value = f"{value:.2f}"
            elif 'cycle' in param:
                formatted_value = f"{int(value)}"
            else:
                formatted_value = f"{value}"
            
            # Clean up parameter name for display
            display_name = param.replace('_', ' ').title()
            row_data[display_name] = formatted_value
        
        # Set headers on first data
        if not self.headers_set:
            headers = list(row_data.keys())
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.headers_set = True
            
            # Auto-resize columns
            self.table.horizontalHeader().setStretchLastSection(True)
            for i in range(len(headers) - 1):
                self.table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        # Add row to table
        self.log_data.append(row_data)
        
        # Limit number of rows
        if len(self.log_data) > self.max_rows:
            self.log_data.pop(0)
        
        # Update table
        self.refresh_table()
    
    def refresh_table(self):
        """Refresh the entire table display"""
        if not self.log_data:
            return
            
        self.table.setRowCount(len(self.log_data))
        
        # Get headers
        headers = list(self.log_data[0].keys()) if self.log_data else []
        
        # Populate table
        for row_idx, row_data in enumerate(self.log_data):
            for col_idx, header in enumerate(headers):
                value = row_data.get(header, '')
                item = QTableWidgetItem(str(value))
                self.table.setItem(row_idx, col_idx, item)
        
        # Scroll to bottom to show latest data
        self.table.scrollToBottom()
    
    def clear_data(self):
        """Clear all logged data"""
        self.log_data.clear()
        self.table.setRowCount(0)
        self.headers_set = False