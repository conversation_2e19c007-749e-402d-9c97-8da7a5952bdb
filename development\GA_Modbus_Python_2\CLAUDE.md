# CLAUDE.md


This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GA Modbus Python Application is a battery management system data logger that communicates with battery hardware via Modbus RTU protocol over serial connections. The application continuously reads battery parameters (cell voltages, temperature, current, SOC, etc.) and logs them to CSV files.

## Development Commands

### Running the Application
```bash
# Activate virtual environment first
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Basic usage (uses config.toml settings)
python3 -m src.modbus_query_test

# List available serial ports
python3 -m src.modbus_query_test --list-ports

# Custom configuration
python3 -m src.modbus_query_test --port COM3 --baudrate 9600 --interval 1.0 --output custom_file.csv
```

### Dependencies
- Install requirements manually as no requirements.txt exists:
  - `pip install pymodbus pyserial tomli`
- Required libraries: pymodbus (3.x), pyserial, tomli

## Code Architecture

### Core Components
- **src/modbus_query_test.py**: Main application with Modbus communication, argument parsing, and continuous logging loop
- **src/ga_modbus_csv_writer.py**: CSV formatter that converts raw Modbus data to structured CSV with proper headers and scaling
- **src/app.py**: PyQt6 GUI application with real-time visualization and CSV viewer
- **config.toml**: TOML configuration file for serial port settings, query intervals, and output paths

### Key Functions
- `src/modbus_query_test.py:395` - `main()`: Entry point with argument parsing and main execution
- `src/modbus_query_test.py:331` - `continuous_logging()`: Main data collection loop
- `src/modbus_query_test.py:173` - `load_config()`: TOML configuration loader
- `src/ga_modbus_csv_writer.py:3` - `write_modbus_data_to_csv()`: CSV data formatter with voltage scaling (mV to V)

### Data Flow
1. Configuration loaded from config.toml or command line arguments
2. Serial connection established using pymodbus ModbusSerialClient
3. Continuous loop reads 33+ Modbus registers (cells, pack voltage, temperature, fuel gauge data)
4. Raw data converted to engineering units and formatted via CSV writer
5. Data appended to timestamped CSV files in log/ directory

### Register Map
Battery data spans registers 10-80+ covering individual cell voltages, pack voltage, temperature sensors, current, SOC, fuel gauge parameters, and battery health metrics.

## Configuration

Edit `config.toml` to modify:
- Serial port settings (port, baudrate, parity, etc.)
- Query interval timing
- Output directory path
- Modbus slave ID

## Output

CSV files are generated in `log/` directory with timestamp-based filenames. Data includes 33 columns covering all battery parameters with proper engineering unit conversion (e.g., mV values scaled to V with 3 decimal precision).

## CSV Viewer Feature

The application includes a dedicated CSV Viewer tab that allows users to:

### Features
- **Directory Browser**: Browse and select CSV files from the `log/` directory or any custom directory
- **Data Table**: View complete CSV data in a sortable table format
- **Chart Visualization**: Plot any numeric column as time series data
- **Multi-series Charts**: Display all cell voltages simultaneously for comparison
- **Statistics Panel**: Automatic calculation of statistical metrics including:
  - Mean, min, max, and standard deviation for all numeric columns
  - Cell voltage balance analysis (average and max cell delta)
  - Dataset summary (row/column counts)

### Usage
1. Navigate to the \"CSV Viewer\" tab in the application
2. Select a CSV file from the file list (defaults to `log/` directory)
3. Double-click or use \"Load Selected File\" to import the data
4. Choose visualization options:
   - Select Y-axis parameter from the dropdown
   - Check \"Show all cell voltages\" for multi-series cell voltage plots
5. Review statistics in the bottom panel

### Requirements
CSV Viewer requires `pandas` and `numpy`. Install with: `pip install pandas numpy`

### GUI Application Launcher
Use the provided launcher script to run the GUI application:
```bash
./run_csv_viewer.sh
```
Or run directly:
```bash
./venv/bin/python3 -m src.app
```