# Changelog

All notable changes to the GA Modbus Python Application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.2.2] - 2025-07-14 (Merge: Enhanced Plotting + Intelligent Logging)

### Added
- **Enhanced Real-Time Plotting** - Interactive plot features for improved data visualization
  - **Legend with Toggle** - Interactive legend for cell voltage plots with click-to-hide/show functionality
  - **Hover Details** - Real-time cursor tracking shows exact values when hovering over plot lines
  - **Crosshair Display** - Visual crosshair follows mouse movement for precise data reading
  - **Color-Coded Legend** - Legend entries match plot line colors for easy identification
  - **Dual Plot Legends** - Both cell voltage and pack voltage/current plots have dedicated legends

- **Parameter Table Delta Column** - Cell voltage balance monitoring
  - **Delta Calculation** - Shows voltage difference from lowest cell in mV
  - **Color Coding** - Red for deltas >50mV, green for normal balance
  - **Real-Time Updates** - Delta values update with each data refresh
  - **Four-Column Layout** - Parameter, Value, Delta, Unit columns for comprehensive data display

- **Debug Logging System** - Comprehensive logging framework for troubleshooting
  - **Centralized Logger**: Standardized logging configuration in `src/utils/logger.py`
  - **File Rotation**: Automatic log file rotation (10MB max, 5 backups) in `logger/` directory
  - **Multiple Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL with proper formatting
  - **Application Logging**: Main app.py logs startup, monitoring operations, and errors
  - **Session Logging**: Session widget and manager log analysis operations and errors
  - **Exception Tracking**: Full stack traces for debugging runtime issues
  - **Console Output**: WARNING+ messages to console, all levels to file
  - **Auto-cleanup**: Optional cleanup of logs older than 30 days

### Changed
- **CSV Logging Default** - CSV logging is now enabled by default for better user experience
  - New installations will have CSV logging automatically enabled
  - Existing users retain their current setting preferences
  - Ensures data capture is active immediately upon first monitoring session
- **Configuration Management** - Application settings now persist to config.toml for better consistency
  - **Serial Port Memory**: Application remembers the last used serial port, baudrate, and slave ID
  - **Settings Persistence**: GUI settings (theme, CSV logging, update interval) saved to config.toml
  - **TOML Integration**: Created comprehensive configuration manager for read/write operations
  - **Cross-Session Continuity**: Connection parameters automatically restored on application restart
  - **Unified Configuration**: Single config.toml file manages both CLI and GUI application settings

### Fixed
- **Session Management Integration** - Fixed sessions tab showing no active session and empty history
  - **Session Lifecycle**: Fixed monitoring to properly create/end sessions when starting/stopping
  - **Real-time Updates**: Sessions tab now shows active session information during monitoring
  - **History Display**: Session history properly populates from stored session data
  - **Session Data**: Monitoring data now correctly updates session statistics and record counts
  - **CSV Association**: CSV files are properly linked to their corresponding sessions
  - **Signal Integration**: Added proper signal connections between SessionManager and UI
  - **Data Format Transformation**: Fixed data format conversion between ModbusWorker and SessionManager
  - **Real-time Duration**: Session duration now updates every 10 seconds for active sessions
  - **CSV Logging Persistence**: CSV logging setting is now saved and restored between application launches
  - **Live Statistics**: Session statistics (record count, file count) update in real-time during monitoring
  - **Periodic Updates**: Added timers for automatic session display updates every 5-10 seconds
  - **Statistics Calculation**: Fixed session statistics not populating by properly integrating SessionManager data with display
  - **Real-time Data**: Session voltage ranges and current averages now display real-time data from active sessions
  - **Enhanced Display**: Statistics columns now show live data from SessionManager instead of just CSV analysis
  - **Session Statistics Fixes**: Resolved multiple session statistics display issues:
    - Duration field now shows actual session duration instead of N/A
    - Event field properly detects charge/discharge/idle events from CSV data analysis
    - Current field displays peak current instead of average current for better insights
    - SOC field extracts and displays latest state-of-charge values from session data
    - Cell Delta Max field shows maximum cell voltage difference during session
  - **Enhanced Session Statistics Display**: Improved data visualization and analysis capabilities:
    - SOC column now shows range format (e.g., "91.0% -> 89.5%") displaying start-to-end state of charge progression
    - Voltage Range displays proper start-to-end voltage progression (e.g., "25.88V -> 26.54V")
    - Cell Delta Max shows range format in mV units (e.g., "5mV -> 44mV") for better readability
    - Added "Cell Group High Delta" column identifying which cell group (e.g., "Cell 4") had the highest voltage during peak delta conditions
    - Fixed CSV column name mapping to support actual data format ("Afe Current", "Fg State Of Charge", "Afe Cell Delta")
    - Added comprehensive voltage scaling logic to convert mV values to V for proper display
    - Enhanced session ID mapping between CSV filenames and session UUIDs for accurate data correlation
- **Sessions Tab Default Behavior** - Enhanced user experience on application startup
  - **Default Tab**: Application now opens in Sessions tab (first tab on the left)
  - **Session Status**: Current session displays "Status: No active session" by default
  - **Auto-load History**: Session history automatically loads when application launches
  - **Improved Workflow**: Better default view for session-based monitoring workflow
  - **CSV File Discovery**: Fixed session widget to scan all CSV files including subdirectories when SessionManager unavailable
  - **Robust Analysis**: Session analysis now works independently of SessionManager for backward compatibility
- **GUI Restoration** - Fixed missing functionality after refactoring
  - **Sessions Tab**: Restored complete session management with enhanced analysis features
    - Added automatic session event type detection (Charge/Discharge/Idle)
    - Implemented voltage range tracking (start -> end voltages)
    - Added peak current analysis and cell delta maximum calculations
    - Enhanced session history table with comprehensive statistics display
    - Restored auto-start configuration with threshold settings
  - **Connection Controls**: Fixed missing baud rate and slave ID controls
    - Restored proper Start/Stop monitoring buttons
    - Added comprehensive connection parameter configuration
    - Fixed CSV logging integration with connection controls
  - **GUI Layout**: Corrected connection control panel layout and functionality

### Architecture
- **Major Refactoring** - Broke down monolithic 2774-line app.py into modular architecture
  - **Communication Layer**: Extracted ModbusWorker to `src/communication/` package (110 lines)
  - **GUI Widgets**: Created focused widget modules in `src/gui/widgets/`:
    - RealTimePlotWidget (106 lines) - Real-time plotting with theme support
    - DataTableWidget (135 lines) - Current data display with color coding
    - CSVLogTableWidget (107 lines) - Real-time logging table
    - ChangelogViewerWidget (128 lines) - Markdown changelog display
    - **SessionWidget (295 lines)** - Enhanced session management with analysis
  - **CSV Viewer**: Modular CSV analysis in `src/gui/csv_viewer/` (200 lines)
  - **Commands Interface**: Firmware commands in `src/gui/commands/` (150 lines)
  - **Theming System**: Centralized styling in `src/themes/styles.py` (200 lines)
  - **Shared Constants**: Enhanced utilities in `src/utils/constants.py` with scaling functions
  - **Main Window**: Simplified from 852 lines to 431 lines using modular imports
  - **Module Entry Point**: Added `src/__main__.py` for proper module execution
  - **Complete Functionality**: All original tabs preserved and enhanced
  - **Improved Architecture**: Better separation of concerns, maintainability, and team development workflow
  - **Runtime Fixes**: Fixed SessionManager method calls and pandas import handling

### Enhanced
- **Session Analysis** - Implemented advanced CSV file analysis
  - **Background Processing**: Multi-threaded session file analysis with progress indicators
  - **Event Classification**: Automatic detection of charging, discharging, and idle sessions
  - **Statistical Analysis**: Voltage progression, peak currents, and cell balance metrics
  - **Enhanced UI**: Color-coded session types and comprehensive data display

### Documentation
- Updated session-identification.md with comprehensive implementation todo list
- Added technical specifications for session event detection and statistics calculation
- Prioritized refactoring of app.py to improve code maintainability

## [1.2.1] - 2025-07-13

### Fixed
- **Modbus Communication** - Corrected slave ID configuration
  - Changed slave ID from 2 to 1 to match firmware source code defaults
  - Ensures proper communication with GA LiFePO4 battery systems
  - Resolves potential connection issues with devices using default firmware settings

## [1.2.0] - 2025-07-13

### Added
- **Commands Tab**: New comprehensive fuel gauge command interface based on GA LiFePO4 Next specifications
  - FET Control: Charge and discharge FET on/off commands
  - System Control: Microcontroller reset, fuel gauge reset, rebalance mode control
  - Fuel Gauge Security: Seal/unseal operations with key management (default keys: 15038901, 1503ABCD)
  - Calibration Commands: Voltage and current calibration with applied value settings
  - Serial Number Management: Set and store serial numbers
  - Real-time command response logging with timestamps
  - Safety warnings and color-coded buttons for dangerous operations
  - Optimized horizontal layout with command groups on left and response log on right
  - Improved button sizing (40px height for FET controls, 35px for other buttons)
  - Enhanced input field sizing (30px height for better usability)
  - Proper spacing between UI elements for better user experience
- **Session Tracking & Auto-Start Logging** - Comprehensive monitoring session management
  - **Session Management** - Track monitoring sessions with metadata, statistics, and file associations
  - **Auto-Start Logging** - Automatically trigger data logging based on current flow thresholds
  - **Session GUI Tab** - Dedicated interface for session configuration and history viewing
  - **Session Data Model** - Complete session schema with trigger types, duration, and statistics
  - **QSettings Integration** - Persistent session storage using existing settings infrastructure
  - **CSV File Association** - Link CSV files to sessions with metadata headers
  - **Real-time Session Indicators** - Visual session status in real-time monitoring view
  - **Auto-Start Configuration** - Configurable charging/discharging current thresholds
  - **Session History** - Browse and analyze past monitoring sessions
  - **Manual & Automatic Sessions** - Support both user-initiated and threshold-triggered sessions

- **Current Reading Validation** - Intelligent filtering to prevent false current readings
  - **False Reading Detection** - Filters out spurious high current values (65.532A etc.) during idle periods
  - **Unsigned to Signed Conversion** - Proper handling of 16-bit Modbus register values
  - **Idle Battery Detection** - Values ≥65500 (near max uint16) automatically set to 0.0A for idle state
  - **High Current Filtering** - Additional safety filter for unreasonable current readings >50A

- **Theme System** - Comprehensive application theming for improved visibility
  - **Dark Theme** - Dark backgrounds with light text for low-light environments
  - **Light Theme** - Light backgrounds with dark text for bright environments  
  - **System Theme** - Automatic OS theme detection and application
  - **Real-time Plot Theming** - Fixed black text on black background visibility issues
  - **Persistent Theme Settings** - Theme preferences saved between sessions
  - **Application-wide Styling** - Consistent theming across all UI components

- **Comprehensive Test Suite** - Unit testing framework for improved code quality
  - **CSV Writer Tests** - Validates cell voltage logging accuracy and CSV format compliance
  - **ModbusWorker Tests** - Tests Modbus communication, data scaling, and error handling
  - **Test Infrastructure** - pytest configuration, fixtures, and mock objects for robust testing
  - **Cell Voltage Validation** - Specific tests ensuring 3-decimal precision for cell voltages (3.456V format)
  - **CSV Header Verification** - Tests proper header creation and append functionality
  - **Missing Data Handling** - Tests graceful handling of missing battery parameters with defaults

### Changed
- **Major Code Refactoring** - Modular architecture for improved maintainability and testability
  - **Modular Structure**: Split monolithic `app.py` (1,556 lines) into focused modules:
    - `src/core/modbus_worker.py` - Threaded Modbus communication
    - `src/widgets/plotting/realtime_plot.py` - Real-time data visualization
    - `src/widgets/data_display/` - Data table and CSV log widgets
    - `src/widgets/viewers/` - CSV and changelog viewer components
    - `src/widgets/settings/` - Configuration management widgets
    - `src/utils/` - Constants, formatters, and utility functions
  - **Improved Testability**: Separated business logic from GUI for unit testing
  - **Better Separation of Concerns**: Each module has single responsibility
  - **Enhanced Maintainability**: Smaller, focused files easier to debug and modify

- **Project Structure Refactoring** - Reorganized Python modules into src directory
  - Moved all Python scripts (`app.py`, `modbus_query_test.py`, `ga_modbus_csv_writer.py`, `check_ports.py`) to `src/` directory
  - Updated import statements to use relative imports within the src package
  - Created `src/__init__.py` to make src a proper Python package
  - Updated all launcher scripts (`run_csv_viewer.sh`, `launch_app.bat`, `launch_app.ps1`, `run_app.bat`) to use module execution (`python -m src.app`)
  - Updated CLAUDE.md documentation to reflect new module paths and execution commands
  - Improved project organization following Python packaging best practices
  - **Legacy Files**: Moved unused legacy applications to `legacy/` directory for cleaner organization

### Fixed
- **Launcher Script Issue** - Fixed run_csv_viewer.sh to work without virtual environment
  - **Virtual Environment Detection** - Script now checks for venv existence before using it
  - **System Python Fallback** - Uses system Python when virtual environment is not available
  - **Cross-platform Compatibility** - Works with both virtual environment and system installations

- **CSV Data Logging Issue** - Corrected voltage value precision in CSV output
  - **Cell Voltage Precision**: Fixed truncation of cell voltages from float values (3.456V) to integers (3) in CSV files
  - **Proper mV Conversion**: CSV now correctly logs cell voltages as mV integers (3456 mV) instead of truncated V values
  - **Data Integrity**: Maintained GUI display of user-friendly V values while preserving raw mV precision in CSV
  - **Legacy Format Compatibility**: Ensured CSV format remains compatible with existing data analysis tools expecting mV values

### Technical Improvements
- **Testing Framework**: Added pytest configuration with comprehensive test coverage
- **Code Quality**: Extracted reusable constants and utility functions
- **Error Handling**: Improved error handling in Modbus communication with proper mocking
- **Data Processing**: Centralized data scaling and formatting logic
- **Development Workflow**: Added test requirements and pytest configuration files

## [1.1.0] - 2025-07-12

### Added
- **Enhanced CSV Viewer** - Advanced file browser and data visualization
  - **Directory Navigation**: Browse battery pack folders (e.g., `log/0533/`, `log/0573/`) with breadcrumb navigation
  - **File Browser**: Visual file list with folder 📁 and CSV 📄 icons for easy identification
  - **Improved Column Detection**: Enhanced support for various CSV formats (Cell1_V, Pack_V, Current columns)
  - **Navigation Controls**: Parent directory (↑), Log Root (🏠) buttons for quick navigation
  - **Multi-series Plotting**: "Show all cell voltages" option for comparative analysis
  - **Prioritized Parameters**: Important battery parameters (Pack_V, Current, SOC) appear first in dropdowns

- **Changelog Viewer Tab** - Built-in documentation and version tracking
  - **Markdown Rendering**: Styled HTML display of CHANGELOG.md with proper formatting
  - **Version Tracking**: Current application version (v1.1.0) displayed in window title and changelog header
  - **Auto-refresh**: Real-time reload of changelog without application restart
  - **File Monitoring**: Shows last modification timestamp of CHANGELOG.md
  - **Styled Display**: Professional formatting with headers, bold text, links, and code highlighting

- **Real-time GUI Application** (`app.py`) - PyQt6-based battery monitoring interface
  - Multi-threaded Modbus communication to prevent GUI blocking
  - Real-time plotting of cell voltages, pack voltage, and current using PyQtGraph
  - Live data table showing current battery parameters with proper unit formatting
  - Persistent user settings storage using QSettings
  
- **CSV Logging Table** - Real-time view of logged data
  - Displays up to 200 rows of historical battery data in tabular format
  - Auto-scrolling to show latest readings
  - Sortable columns with alternating row colors
  - Clear log functionality for data management
  
- **Settings & Threshold Monitoring**
  - Configurable voltage monitoring thresholds with color-coded warnings
  - **Cell Delta Warning Threshold**: Default 30mV, triggers red text when cell voltage spread exceeds limit
  - **Pack Voltage Thresholds**: Min 22V, Max 28.6V with color coding for out-of-range values
  - Save/Reset functionality with persistent storage
  
- **Color-Coded Safety Monitoring**
  - Cell voltages: Green when delta ≤ threshold, Red when delta > threshold
  - Pack voltage: Green when within min/max range, Red when outside safe limits
  - Real-time visual feedback for immediate issue identification
  
- **Enhanced Tabbed Interface**
  - **Real-time View**: Live plots and current data table
  - **CSV Log**: Historical data table with filtering capabilities
  - **CSV Viewer**: Advanced file browser and data visualization
  - **Changelog**: Built-in documentation and version history
  - **Settings**: Threshold configuration and application preferences
  
- **Enhanced Data Handling**
  - Automatic unit conversion (mV to V, mA to A, etc.)
  - Proper decimal formatting for different parameter types
  - Timestamp-based data organization
  - CSV export integration with existing logging system

### Technical Improvements
- Multi-threaded architecture separating GUI from Modbus communication
- Robust error handling with user feedback via status messages
- Automatic serial port detection and configuration persistence
- Memory-efficient data storage with configurable history limits
- Cross-platform compatibility using PyQt6 framework

### Dependencies
- PyQt6 - GUI framework
- PyQtGraph - Real-time plotting
- pymodbus - Modbus RTU communication
- pyserial - Serial port communication
- tomli - Configuration file parsing

## [1.0.0] - Initial Release

### Added
- Command-line Modbus RTU data logger (`modbus_query_test.py`)
- CSV data formatting and export (`ga_modbus_csv_writer.py`)
- TOML-based configuration system (`config.toml`)
- Battery register mapping for 8-cell lithium pack monitoring
- Continuous data logging with configurable intervals
- Support for cell voltages, pack voltage, temperature, current, and fuel gauge data