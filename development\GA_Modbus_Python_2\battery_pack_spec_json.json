{"product_info": {"part_number": "XGD-JGTFR18650-X72PC", "document_number": "MPS-XGD-JGTFR18650-X72PC", "revision": "X4", "date": "2022-01-21", "manufacturer": "Custom Power", "company_address": "FOUNTAIN VALLEY, CA 92708", "company_phone": "(*************", "company_website": "www.custompower.com"}, "battery_configuration": {"type": "Lithium-Iron Phosphate (LiFePO4)", "configuration": "8S9P", "series_cells": 8, "parallel_cells": 9, "total_cells": 72, "cell_type": "18650"}, "electrical_specifications": {"nominal_voltage": {"value": 25.6, "unit": "V"}, "capacity": {"value": 14.4, "unit": "Ah", "type": "typical"}, "energy": {"value": 368.6, "unit": "Wh"}, "charging": {"standard_current": {"value": 6, "unit": "A"}, "fast_charge_current": {"value": 10, "unit": "A"}, "termination_voltage": {"value": 28.4, "unit": "V", "per_cell": 3.55}, "taper_current": {"value": 144, "unit": "mA", "description": "C/100"}}, "discharging": {"standard_current": {"value": 17, "unit": "A"}, "max_continuous_current": {"value": 20, "unit": "A"}, "peak_current": {"value": 45, "unit": "A", "duration": "5 seconds"}}, "voltage_limits": {"system_minimum": {"value": 23.6, "unit": "V", "per_cell": 2.95}}}, "protection_features": {"cell_overvoltage": {"cutoff": {"value": 3.9, "tolerance": 0.03, "unit": "V"}, "recovery": {"value": 3.6, "tolerance": 0.03, "unit": "V"}}, "cell_undervoltage": {"cutoff": {"value": 2.75, "tolerance": 0.02, "unit": "V"}, "recovery": {"value": 2.85, "tolerance": 0.02, "unit": "V"}}, "discharge_overcurrent": {"level_1": {"current": 47, "unit": "A", "duration": "10 seconds"}, "level_2": {"current": 50, "unit": "A", "duration": "80 msec"}}, "secondary_protection": {"type": "<PERSON><PERSON>", "rating": 30, "unit": "A", "characteristic": "Slow Blow"}}, "temperature_specifications": {"operation": {"charge_range": {"min": 0, "max": 45, "unit": "°C"}, "discharge_range": {"min": -20, "max": 60, "unit": "°C"}}, "storage": {"recommended_range": {"min": -20, "max": 55, "unit": "°C"}}, "thermistor": {"type": "NTC", "model": "Semitec 103AT", "resistance": {"value": 10, "unit": "kΩ", "tolerance": "±1%"}, "b_value": {"value": 3435, "unit": "K", "tolerance": "±1%"}}}, "physical_specifications": {"dimensions": {"length": 206.0, "width": 70.3, "height": 133.35, "connector_length": 125.0, "unit": "mm"}, "weight": {"pack_only": {"value": 3200, "unit": "grams"}, "with_foam_pad": {"value": 3800, "unit": "grams"}}}, "connector_pinout": {"power_connector": {"part_number": "Tyco 213426-1", "mating_part": "Tyco 193643-1", "pins": {"A": {"wire_color": "RED", "gauge": "10AWG", "spec": "M22759/12", "function": "PACK+"}, "C": {"wire_color": "BLACK", "gauge": "10AWG", "spec": "M22759/12", "function": "PACK-"}}}, "auxiliary_connector": {"part_number": "Tyco 1-66360-4", "pins": {"1": {"wire_color": "WHITE", "gauge": "14AWG", "spec": "M22759/12", "function": "N/A"}, "9": {"wire_color": "BLACK", "gauge": "14AWG", "spec": "M22759/12", "function": "12"}}}, "communication_connector": {"part_number": "Tyco 66565-4", "pins": {"3": {"wire_color": "WHITE", "gauge": "20AWG", "spec": "M22759/12", "function": "Pack Recovery"}, "6": {"wire_color": "BROWN", "gauge": "20AWG", "spec": "M22759/12", "function": "Wake Bit"}, "7": {"wire_color": "WHITE", "gauge": "20AWG", "spec": "M27500", "function": "RX RS232"}, "8": {"wire_color": "ORANGE", "gauge": "20AWG", "spec": "M22759/12", "function": "Sig Gnd"}, "10": {"wire_color": "WHITE/BLUE", "gauge": "20AWG", "spec": "M27500", "function": "TX RS232"}, "11": {"wire_color": "WHITE/ORANGE", "gauge": "20AWG", "spec": "M27500", "function": "GND RS232"}}}}, "control_system": {"microcontroller": "32-bit ARM M0", "afe_chip": "bq76930", "fuel_gauge": "bq34z100-R1", "functions": ["Digital control of AFE", "Bridge between fuel gauge and AFE", "MODBUS interface management", "Register updates approximately once per second"], "wake_functionality": {"enable_method": "<PERSON><PERSON> Wake Bit low", "disable_method": "Leave Wake Bit floating", "modes": ["Sleep mode", "Normal operating mode"]}, "recovery_pin": {"purpose": "Recover fully depleted battery pack", "procedure": "Connect to 25V, 500mA power supply for 5-10 minutes", "threshold": "Pack voltage below 23V"}}, "modbus_communication": {"protocol": "Modbus RTU", "interface": "RS232", "communication_cycle": "Asynchronous", "parameters": {"baud_rate": 9600, "data_bits": 8, "parity": "even", "stop_bits": 1}, "max_connections": 1, "slave_address": "0x02", "function_code": "0x03", "max_registers_per_read": 16}, "modbus_registers": {"afe_registers": [{"address": 30010, "name": "System Status", "afe_register": "0x00"}, {"address": 30011, "name": "CELL Balancing_1", "afe_register": "0x01"}, {"address": 30012, "name": "CELL Balancing_2", "afe_register": "0x02"}, {"address": 30013, "name": "CELL Balancing_3", "afe_register": "0x03"}, {"address": 30014, "name": "SYS_CTRL1", "afe_register": "0x04"}, {"address": 30015, "name": "SYS_CTRL2", "afe_register": "0x05"}, {"address": 30016, "name": "Protect_1", "afe_register": "0x06"}, {"address": 30017, "name": "Protect_2", "afe_register": "0x07"}, {"address": 30018, "name": "Protect_3", "afe_register": "0x08"}, {"address": 30019, "name": "Over Voltage", "afe_register": "0x09"}, {"address": 30020, "name": "Under Voltage", "afe_register": "0x0A"}], "cell_voltage_registers": [{"cell": 1, "low_byte": {"address": 30022, "afe_register": "0x0D"}, "high_byte": {"address": 30023, "afe_register": "0x0C"}}, {"cell": 2, "low_byte": {"address": 30024, "afe_register": "0x0F"}, "high_byte": {"address": 30025, "afe_register": "0x0E"}}, {"cell": 3, "low_byte": {"address": 30026, "afe_register": "0x11"}, "high_byte": {"address": 30027, "afe_register": "0x10"}}, {"cell": 4, "low_byte": {"address": 30028, "afe_register": "0x13"}, "high_byte": {"address": 30029, "afe_register": "0x12"}}, {"cell": 5, "low_byte": {"address": 30030, "afe_register": "0x15"}, "high_byte": {"address": 30031, "afe_register": "0x14"}}, {"cell": 6, "low_byte": {"address": 30032, "afe_register": "0x17"}, "high_byte": {"address": 30033, "afe_register": "0x16"}}, {"cell": 7, "low_byte": {"address": 30034, "afe_register": "0x19"}, "high_byte": {"address": 30035, "afe_register": "0x18"}}, {"cell": 8, "low_byte": {"address": 30036, "afe_register": "0x1B"}, "high_byte": {"address": 30037, "afe_register": "0x1A"}}], "pack_measurements": [{"address": 30052, "name": "Pack Volt Low Byte", "afe_register": "0x2B"}, {"address": 30053, "name": "Pack Volt High Byte", "afe_register": "0x2A"}, {"address": 30054, "name": "AFE TS1_Low Byte", "afe_register": "0x2D"}, {"address": 30055, "name": "AFE TS1_High Byte", "afe_register": "0x2C"}, {"address": 30056, "name": "AFE TS2_Low Byte", "afe_register": "0x2F"}, {"address": 30057, "name": "AFE TS2_High Byte", "afe_register": "0x2E"}, {"address": 30060, "name": "AFE Coulomb counter low Byte", "afe_register": "0x33"}, {"address": 30061, "name": "AFE Coulomb counter high Byte", "afe_register": "0x32"}], "fuel_gauge_registers": [{"address": 30064, "name": "State of Charge", "fg_register": "0x02"}, {"address": 30065, "name": "<PERSON>", "fg_register": "0x03"}, {"address": 30069, "name": "Full Charge Capacity high byte", "fg_register": "0x07"}, {"address": 30070, "name": "Battery Voltage low byte", "fg_register": "0x08"}, {"address": 30071, "name": "Battery Voltage high byte", "fg_register": "0x09"}, {"address": 30072, "name": "Avg Current Low Byte", "fg_register": "0x0A"}, {"address": 30073, "name": "Avg Current High Byte", "fg_register": "0x0B"}, {"address": 30074, "name": "Pack Temp Low Byte", "fg_register": "0x0C"}, {"address": 30075, "name": "Pack Temp High Byte", "fg_register": "0x0D"}, {"address": 30076, "name": "FG Flag A", "fg_register": "0x0E"}, {"address": 30077, "name": "FG Flag B", "fg_register": "0x0F"}, {"address": 30078, "name": "Current Low Byte", "fg_register": "0x10"}, {"address": 30079, "name": "Current High Byte", "fg_register": "0x11"}, {"address": 30080, "name": "FG Flag C", "fg_register": "0x12"}, {"address": 30081, "name": "FG Flag D", "fg_register": "0x13"}, {"address": 30082, "name": "AVG Time to Empty low byte", "fg_register": "0x18"}, {"address": 30083, "name": "AVG Time to Empty high byte", "fg_register": "0x19"}, {"address": 30084, "name": "AVG Time to Full low byte", "fg_register": "0x1A"}, {"address": 30085, "name": "AVG Time to Full high byte", "fg_register": "0x1B"}, {"address": 30086, "name": "FG Internal Temp low byte", "fg_register": "0x2A"}, {"address": 30087, "name": "FG Internal Temp high byte", "fg_register": "0x2B"}, {"address": 30088, "name": "Cycle Count low byte", "fg_register": "0x2C"}, {"address": 30089, "name": "Cycle Count high byte", "fg_register": "0x2D"}, {"address": 30090, "name": "State of health low byte", "fg_register": "0x2E"}, {"address": 30091, "name": "State of health high byte", "fg_register": "0x2F"}, {"address": 30096, "name": "Pack Configuration low byte", "fg_register": "0x3A"}, {"address": 30097, "name": "Pack Configuration high byte", "fg_register": "0x3B"}], "lifetime_data_registers": [{"address": 30110, "name": "LifeTime Max Pack Volt Low Byte", "fg_register": "0x59+8"}, {"address": 30111, "name": "LifeTime Max Pack Volt High Byte", "fg_register": "0x59+9"}, {"address": 30112, "name": "LifeTime Min Pack Volt Low Byte", "fg_register": "0x59+10"}, {"address": 30113, "name": "LifeTime Min Pack Volt High Byte", "fg_register": "0x59+11"}], "firmware_registers": [{"address": 30114, "name": "uC Rev", "storage": "EEPROM"}, {"address": 30115, "name": "FG Rev", "storage": "EEPROM"}], "voltage_divider_registers": [{"address": 30117, "name": "Voltage Divider Low Byte"}, {"address": 30118, "name": "Voltage Divider High Byte"}]}, "environmental_specifications": {"operating_temperature": {"standard_charging": {"min": 0, "max": 55, "unit": "°C"}, "discharging": {"min": -20, "max": 55, "unit": "°C"}}, "storage_temperature": {"min": -20, "max": 55, "unit": "°C"}}, "packaging": {"method": "Thick foam pad with PVC shrink", "anti_chaffing": "Required on all wires, material specified by customer"}, "compliance": {"transportation": "UN Manual of Tests and Criteria, Part III, Sub-Section 38.3"}, "shelf_life": {"self_discharge_rate": {"cells": "1-2% per month at 25°C", "protection_circuit": "1-2% per month additional", "temperature_effect": "Doubles every 10°C increase"}, "storage_guidelines": ["Do not exceed manufacturer's temperature limits", "Avoid individual cell voltage below 2.0V", "Store at 60-80% SOC for extended periods", "Implement charge maintenance every 3-5 months"], "expected_cycle_life": {"cycles": "1200-1500", "depth_of_discharge": "100%"}}, "labeling": {"label_1": {"location": "Battery pack", "includes": ["Company info", "Part numbers", "Specifications", "Date code (MM/YY)"]}, "label_2": {"location": "Shipping box", "includes": ["Part numbers", "Manufacturing date", "Quantity"]}}, "revision_history": [{"revision": "X1", "date": "2021-06-04", "author": "<PERSON><PERSON>", "changes": "Pre-release"}, {"revision": "X2", "date": "2021-06-16", "author": "<PERSON><PERSON>", "changes": "Updated pack weight with foam pad"}, {"revision": "X3", "date": "2021-10-28", "author": "<PERSON><PERSON>", "changes": "2.1: System Min Volt, 2.2: CUV, DOC2; added-pin 3-table 1"}, {"revision": "X4", "date": "2022-01-21", "author": "Unknown", "changes": "2.5: Schematic and board layout"}], "notes": {"calibration": "Current, Voltages and Temperature of the Fuel Gauge are calibrated", "register_data_availability": "Some register data may not be available in seal mode", "reference": "See TI bq34z100-R1 data sheet for more details regarding fuel gauge registers"}}