"""
CSV writer for Modbus battery data with proper formatting and headers.
"""

import os
from datetime import datetime
from typing import Dict, Any, Optional

from .utils.formatters import format_parameter_value


def write_modbus_data_to_csv(data: Dict[str, Any], filepath: str) -> None:
    """
    Write modbus data to CSV with specific formatting and headers.
    Handles missing fields gracefully with default values.
    
    Args:
        data: Dictionary containing battery parameter data
        filepath: Path to the CSV file to write to
    """
    headers = [
        "Timestamp", "Cell1_V", "Cell2_V", "Cell3_V", "Cell4_V", "Cell5_V", "Cell6_V",
        "Cell7_V", "Cell8_V", "Pack_V", "Cell_Delta_V", "Temp1", "Temp2", "Current",
        "SOC", "FG_Voltage", "FG_Current", "FG_Temp", "Remaining_Cap", "Full_Cap",
        "Design_Cap", "Avg_Current", "Time_Empty", "Time_Full", "Internal_Temp",
        "Cycle_Count", "SOH", "Charging_V", "Charging_I", "Max_Temp", "Min_Temp",
        "Max_Chg_Curr", "Max_Dsg_Curr"
    ]

    def safe_get_raw(key: str, default: int = 0) -> int:
        """Safely get raw integer value in mV, return default if missing"""
        try:
            value = data.get(key, default)
            # Convert back to mV for CSV logging (multiply V by 1000 to get mV)
            if 'volt' in key and 'cell' in key:
                # Cell voltages: convert from V back to mV for CSV
                return int(value * 1000)
            elif key == 'afe_pack_volt':
                # Pack voltage: convert from V back to mV for CSV
                return int(value * 1000)
            elif key == 'fg_voltage' or key == 'fg_charging_voltage':
                # Fuel gauge voltages: convert from V back to mV for CSV
                return int(value * 1000)
            else:
                # Other values: use as-is
                return int(value)
        except (ValueError, TypeError):
            return default

    def safe_get(key: str, default: Any = 0) -> Any:
        """Safely get value, return default if missing"""
        return data.get(key, default)

    # Use timestamp from data if available, otherwise current time
    timestamp = data.get('timestamp', datetime.now())
    if isinstance(timestamp, datetime):
        timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    else:
        timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    values = [
        timestamp_str,
        safe_get_raw('afe_cell_volt1'),
        safe_get_raw('afe_cell_volt2'),
        safe_get_raw('afe_cell_volt3'),
        safe_get_raw('afe_cell_volt4'),
        safe_get_raw('afe_cell_volt5'),
        safe_get_raw('afe_cell_volt6'),
        safe_get_raw('afe_cell_volt7'),
        safe_get_raw('afe_cell_volt8'),
        safe_get_raw('afe_pack_volt'),
        safe_get_raw('afe_cell_volt_delta'),
        safe_get('afe_temp1'),
        safe_get('afe_temp2'),
        safe_get('afe_current'),
        safe_get('fg_state_of_charge'),
        safe_get_raw('fg_voltage'),
        safe_get('fg_current'),
        safe_get('fg_temperature'),
        safe_get('fg_remaining_capacity'),
        safe_get('fg_full_charge_cap'),
        safe_get('fg_design_capacity'),
        safe_get('fg_average_current'),
        safe_get('fg_time_to_empty'),
        safe_get('fg_time_to_full'),
        safe_get('fg_internal_temp'),
        safe_get('fg_cycle_count'),
        safe_get('fg_state_of_health'),
        safe_get_raw('fg_charging_voltage'),
        safe_get('fg_charging_current'),
        safe_get('fg_lifetime_max_temp'),
        safe_get('fg_lifetime_min_temp'),
        safe_get('fg_lifetime_max_chg'),
        safe_get('fg_lifetime_max_dsg')
    ]

    # Ensure directory exists
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    write_header = not os.path.exists(filepath)

    with open(filepath, 'a', newline='') as f:
        if write_header:
            f.write(','.join(headers) + '\n')
        f.write(','.join(map(str, values)) + '\n')


def add_session_metadata_to_csv(filepath: str, session_data: Dict[str, Any]) -> None:
    """
    Add session metadata as comments at the beginning of a CSV file.
    
    Args:
        filepath: Path to the CSV file
        session_data: Session metadata dictionary
    """
    if not os.path.exists(filepath):
        return
    
    # Read existing content
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Create session metadata comments
    metadata_lines = [
        f"# Session ID: {session_data.get('session_id', 'N/A')}",
        f"# Session Start: {session_data.get('start_time', 'N/A')}",
        f"# Trigger Type: {session_data.get('trigger_type', 'manual')}",
        f"# Connection: {session_data.get('connection_params', {}).get('port', 'N/A')} @ {session_data.get('connection_params', {}).get('baudrate', 'N/A')} baud",
        f"# Generated by GA Modbus Python Application",
        ""  # Empty line
    ]
    
    # Write metadata + existing content
    with open(filepath, 'w') as f:
        f.write('\n'.join(metadata_lines))
        f.write(content)