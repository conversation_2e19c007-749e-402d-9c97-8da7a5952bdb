#!/usr/bin/env python3
"""
Check available COM ports on the system
"""
import serial.tools.list_ports

def list_available_ports():
    """List all available serial ports"""
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("No COM ports found on this system.")
        return
    
    print("Available COM ports:")
    print("-" * 50)
    for port in ports:
        print(f"Port: {port.device}")
        print(f"Description: {port.description}")
        print(f"Hardware ID: {port.hwid}")
        print("-" * 50)

if __name__ == "__main__":
    list_available_ports()
