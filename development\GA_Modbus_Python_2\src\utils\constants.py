"""
Constants and configuration for the GA Battery Management System.
"""

# Application version
APP_VERSION = "1.2.1"

# Register map for Modbus registers
REGISTER_MAP = {
    10: "afe_cell_volt1",
    11: "afe_cell_volt2",
    12: "afe_cell_volt3",
    13: "afe_cell_volt4",
    14: "afe_cell_volt5",
    15: "afe_cell_volt6",
    16: "afe_cell_volt7",
    17: "afe_cell_volt8",
    18: "afe_pack_volt",
    19: "afe_cell_volt_delta",
    20: "afe_temp1",
    21: "afe_temp2",
    22: "afe_current",
    23: "afe_adc_gain",
    24: "afe_adc_offset",
    25: "afe_ov_limit",
    26: "afe_uv_limit",
    27: "fg_state_of_charge",
    28: "fg_voltage",
    29: "fg_current",
    30: "fg_temperature",
    31: "fg_remaining_capacity",
    32: "fg_full_charge_cap",
    33: "fg_design_capacity",
    34: "fg_average_current",
    35: "fg_time_to_empty",
    36: "fg_time_to_full",
    37: "fg_internal_temp",
    38: "fg_cycle_count",
    39: "fg_state_of_health",
    40: "fg_charging_voltage",
    41: "fg_charging_current",
    42: "fg_lifetime_max_temp",
    43: "fg_lifetime_min_temp",
    44: "fg_lifetime_max_chg",
    45: "fg_lifetime_max_dsg"
}

# Default Modbus settings
DEFAULT_MODBUS_SETTINGS = {
    'baudrate': 9600,
    'parity': 'E',
    'stopbits': 1,
    'bytesize': 8,
    'timeout': 2,
    'retries': 3,
    'slave_id': 2,
    'interval': 1.0
}

# Default GUI settings
DEFAULT_SETTINGS = {
    'cell_delta_threshold': 50.0,  # mV
    'pack_volt_min': 20.0,  # V
    'pack_volt_max': 30.0,  # V
    'temp_warning_threshold': 60.0,  # °C
    'current_max': 10.0,  # A
    'max_plot_points': 300,
    'csv_log_max_rows': 100
}

# Data scaling factors
SCALING_FACTORS = {
    'voltage_to_mv': 1000,  # Convert V to mV
    'current_to_ma': 1000,  # Convert A to mA
    'temperature_resolution': 10.0  # 0.1°C resolution
}

# Colors for plotting
PLOT_COLORS = [
    '#ff0000', '#00ff00', '#0000ff', '#ffff00',
    '#ff00ff', '#00ffff', '#ffa500', '#800080'
]

# Default directories
DEFAULT_LOG_DIR = "log"
DEFAULT_CONFIG_FILE = "config.toml"

# Data storage
MAX_DATA_POINTS = 300  # 5 minutes at 1Hz

# Default connection parameters
DEFAULT_PORT = None
DEFAULT_BAUDRATE = 9600
DEFAULT_PARITY = 'E'
DEFAULT_SLAVE_ID = 1
DEFAULT_INTERVAL = 1.0

# Value scaling functions
def scale_voltage(value):
    """Convert mV to V"""
    return value / 1000.0

def scale_current(value):
    """Convert mA to A with signed 16-bit handling"""
    # Handle signed 16-bit values
    if value > 32767:
        signed_value = value - 65536
    else:
        signed_value = value
    
    # Filter false readings near max uint16 values
    if value >= 65500 or abs(signed_value) > 50000:
        return 0.0
    
    return signed_value / 1000.0

def scale_temperature(value):
    """Convert temperature with 0.1°C resolution"""
    return value / 10.0