"""
Changelog viewer widget for displaying CHANGELOG.md with version tracking
"""

import os
import re
from datetime import datetime
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt6.QtGui import QFont

from ...utils.constants import APP_VERSION


class ChangelogViewerWidget(QWidget):
    """Widget for viewing CHANGELOG.md with version tracking"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_changelog()
    
    def setup_ui(self):
        """Setup the changelog viewer UI"""
        layout = QVBoxLayout()
        
        # Header with version info
        header_layout = QHBoxLayout()
        
        version_label = QLabel(f"<h2>📋 Changelog - Current Version: {APP_VERSION}</h2>")
        header_layout.addWidget(version_label)
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.load_changelog)
        refresh_btn.setMaximumWidth(100)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Changelog content viewer
        self.changelog_text = QTextEdit()
        self.changelog_text.setReadOnly(True)
        self.changelog_text.setFont(QFont("Consolas", 10))  # Monospace font for better markdown
        layout.addWidget(self.changelog_text)
        
        # Footer with file info
        self.footer_label = QLabel("CHANGELOG.md")
        self.footer_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.footer_label)
        
        self.setLayout(layout)
    
    def load_changelog(self):
        """Load and display the CHANGELOG.md file"""
        changelog_path = "CHANGELOG.md"
        
        try:
            if os.path.exists(changelog_path):
                with open(changelog_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Convert basic markdown to HTML for better display
                html_content = self.markdown_to_html(content)
                self.changelog_text.setHtml(html_content)
                
                # Update footer with file info
                file_stat = os.stat(changelog_path)
                modified_time = datetime.fromtimestamp(file_stat.st_mtime)
                self.footer_label.setText(
                    f"CHANGELOG.md - Last modified: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                self.changelog_text.setPlainText("CHANGELOG.md not found in the project directory.")
                self.footer_label.setText("CHANGELOG.md - File not found")
                
        except Exception as e:
            self.changelog_text.setPlainText(f"Error loading CHANGELOG.md: {e}")
            self.footer_label.setText("CHANGELOG.md - Error loading file")
    
    def markdown_to_html(self, markdown_text):
        """Convert basic markdown to HTML for display"""
        html = markdown_text
        
        # Headers
        html = html.replace('### ', '<h3>')
        html = html.replace('## ', '<h2>')
        html = html.replace('# ', '<h1>')
        
        # Close header tags at end of line
        lines = html.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('<h1>'):
                lines[i] = line + '</h1>'
            elif line.startswith('<h2>'):
                lines[i] = line + '</h2>'
            elif line.startswith('<h3>'):
                lines[i] = line + '</h3>'
        
        html = '\n'.join(lines)
        
        # Bold text
        html = html.replace('**', '<b>', 1).replace('**', '</b>', 1)
        while '**' in html:
            html = html.replace('**', '<b>', 1).replace('**', '</b>', 1)
        
        # Lists
        html = html.replace('- ', '• ')
        
        # Code blocks (basic)
        html = html.replace('`', '<code>').replace('`', '</code>')
        
        # Line breaks for better formatting
        html = html.replace('\n', '<br>')
        
        # Links
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        html = re.sub(link_pattern, r'<a href="\2">\1</a>', html)
        
        # Add basic styling
        styled_html = f"""
        <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
        h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; }}
        h3 {{ color: #7f8c8d; }}
        code {{ background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
        a {{ color: #3498db; }}
        </style>
        <body>{html}</body>
        """
        
        return styled_html