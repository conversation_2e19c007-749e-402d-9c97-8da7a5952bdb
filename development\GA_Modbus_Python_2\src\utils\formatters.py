"""
Data formatting utilities for the GA Battery Management System.
"""

from typing import Any, Dict


def format_voltage(value: float) -> str:
    """Format voltage value to 3 decimal places."""
    return f"{value:.3f}"


def format_current(value: float) -> str:
    """Format current value to 3 decimal places."""
    return f"{value:.3f}"


def format_temperature(value: float) -> str:
    """Format temperature value to 1 decimal place."""
    return f"{value:.1f}"


def format_percentage(value: float) -> str:
    """Format percentage value to 1 decimal place."""
    return f"{value:.1f}"


def format_capacity(value: float) -> str:
    """Format capacity value to 2 decimal places."""
    return f"{value:.2f}"


def format_integer(value: float) -> str:
    """Format integer value."""
    return f"{int(value)}"


def get_parameter_unit(param_name: str) -> str:
    """Get the appropriate unit for a parameter based on its name."""
    if 'volt' in param_name:
        return 'V'
    elif 'current' in param_name:
        return 'A'
    elif 'temp' in param_name:
        return '°C'
    elif 'charge' in param_name:
        return '%'
    elif 'capacity' in param_name:
        return 'Ah'
    elif 'cycle' in param_name:
        return 'cycles'
    else:
        return ''


def format_parameter_value(param_name: str, value: Any) -> str:
    """Format a parameter value based on its type and name."""
    if not isinstance(value, (int, float)):
        return str(value)
    
    if 'volt' in param_name:
        return format_voltage(value)
    elif 'current' in param_name:
        return format_current(value)
    elif 'temp' in param_name:
        return format_temperature(value)
    elif 'charge' in param_name:
        return format_percentage(value)
    elif 'capacity' in param_name:
        return format_capacity(value)
    elif 'cycle' in param_name:
        return format_integer(value)
    else:
        return str(value)


def get_display_name(param_name: str) -> str:
    """Convert parameter name to a human-readable display name."""
    return param_name.replace('_', ' ').title()


def scale_modbus_value(param_name: str, raw_value: int) -> float:
    """Scale raw Modbus register value to engineering units for display purposes only.
    Note: CSV logging uses raw integer values for data integrity."""
    if 'volt' in param_name and 'cell' in param_name:
        # Cell voltages in mV, convert to V for display
        return raw_value / 1000.0
    elif param_name == 'afe_pack_volt':
        # Pack voltage in mV, convert to V for display
        return raw_value / 1000.0
    elif 'current' in param_name:
        # Current in mA, convert to A for display
        return raw_value / 1000.0
    elif 'temp' in param_name:
        # Temperature conversion (assuming 0.1°C resolution)
        return raw_value / 10.0
    else:
        return float(raw_value)


def get_raw_value_for_csv(param_name: str, scaled_value: float) -> int:
    """Convert scaled engineering value back to raw integer for CSV logging.
    This maintains compatibility with legacy CSV format."""
    if 'volt' in param_name and ('cell' in param_name or param_name == 'afe_pack_volt' or param_name == 'fg_voltage' or param_name == 'fg_charging_voltage'):
        # Convert V back to mV (raw integer)
        return int(scaled_value * 1000.0)
    elif 'current' in param_name:
        # Convert A back to mA (raw integer)
        return int(scaled_value * 1000.0)
    elif 'temp' in param_name:
        # Convert back to raw temperature units
        return int(scaled_value * 10.0)
    else:
        return int(scaled_value)