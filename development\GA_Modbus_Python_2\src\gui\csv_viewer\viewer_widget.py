"""
CSV Viewer widget for analyzing CSV files
"""

import os
from PyQt6.QtWidgets import (
    QWidget, QVB<PERSON>Layout, QHBoxLayout, QLabel, QPushButton, 
    QListWidget, QFileDialog, QMessageBox, QTabWidget,
    QTableWidget, QTableWidgetItem, QComboBox, QCheckBox
)

from ...utils.constants import DEFAULT_LOG_DIR

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False


class CSVViewerWidget(QWidget):
    """Widget for viewing and analyzing CSV files"""
    
    def __init__(self):
        super().__init__()
        self.current_csv_data = None
        self.current_directory = DEFAULT_LOG_DIR
        self.setup_ui()
        self.refresh_file_list()
    
    def setup_ui(self):
        """Setup the CSV viewer UI"""
        layout = QHBoxLayout()
        
        # Left panel: File browser
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        left_panel.setMaximumWidth(300)
        
        # Directory controls
        dir_label = QLabel(f"Directory: {self.current_directory}")
        left_layout.addWidget(dir_label)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_directory)
        left_layout.addWidget(browse_btn)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.itemDoubleClicked.connect(self.load_selected_file)
        left_layout.addWidget(self.file_list)
        
        load_btn = QPushButton("Load Selected File")
        load_btn.clicked.connect(self.load_selected_file)
        left_layout.addWidget(load_btn)
        
        # Right panel: CSV data display
        right_panel = QWidget()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        # CSV data table
        self.csv_table = QTableWidget()
        right_layout.addWidget(self.csv_table)
        
        # Status
        self.status_label = QLabel("No file loaded")
        right_layout.addWidget(self.status_label)
        
        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)
        
        self.setLayout(layout)
    
    def browse_directory(self):
        """Browse for directory"""
        directory = QFileDialog.getExistingDirectory(self, "Select Directory", self.current_directory)
        if directory:
            self.current_directory = directory
            self.refresh_file_list()
    
    def refresh_file_list(self):
        """Refresh the file list"""
        self.file_list.clear()
        
        if not os.path.exists(self.current_directory):
            os.makedirs(self.current_directory, exist_ok=True)
        
        try:
            files = [f for f in os.listdir(self.current_directory) if f.endswith('.csv')]
            files.sort(reverse=True)  # Show newest first
            
            for file in files:
                self.file_list.addItem(file)
                
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error reading directory: {e}")
    
    def load_selected_file(self):
        """Load the selected CSV file"""
        current_item = self.file_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a file first")
            return
            
        filename = current_item.text()
        filepath = os.path.join(self.current_directory, filename)
        
        if not PANDAS_AVAILABLE:
            QMessageBox.warning(self, "Error", "pandas is required for CSV viewing. Please install with: pip install pandas")
            return
        
        try:
            # Load CSV data
            self.current_csv_data = pd.read_csv(filepath)
            
            # Display in table
            self.display_csv_data()
            
            self.status_label.setText(f"Loaded: {filename} ({len(self.current_csv_data)} rows, {len(self.current_csv_data.columns)} columns)")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading CSV file: {e}")
    
    def display_csv_data(self):
        """Display CSV data in the table"""
        if self.current_csv_data is None:
            return
        
        df = self.current_csv_data
        
        # Set up table
        self.csv_table.setRowCount(len(df))
        self.csv_table.setColumnCount(len(df.columns))
        self.csv_table.setHorizontalHeaderLabels(df.columns.tolist())
        
        # Fill table with data
        for row in range(len(df)):
            for col in range(len(df.columns)):
                value = str(df.iloc[row, col])
                item = QTableWidgetItem(value)
                self.csv_table.setItem(row, col, item)
        
        # Auto-resize columns
        self.csv_table.resizeColumnsToContents()