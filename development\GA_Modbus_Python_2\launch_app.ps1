# GA Battery Management System Launcher
# PowerShell script to launch the app with virtual environment

Write-Host "Starting GA Battery Management System..." -ForegroundColor Green
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot

# Check if src/app.py exists
if (-not (Test-Path "src\app.py")) {
    Write-Host "ERROR: src\app.py not found!" -ForegroundColor Red
    Write-Host "Please make sure src\app.py is in the current directory."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check for virtual environment
$pythonExe = $null

if (Test-Path "venv\Scripts\python.exe") {
    Write-Host "Found Windows-style virtual environment..." -ForegroundColor Yellow
    $pythonExe = "venv\Scripts\python.exe"
} elseif (Test-Path "venv\bin\python") {
    Write-Host "Found Unix-style virtual environment..." -ForegroundColor Yellow
    $pythonExe = "venv\bin\python"
} else {
    Write-Host "WARNING: Virtual environment not found, using system Python..." -ForegroundColor Yellow
    $pythonExe = "python"
}

# Launch the application
Write-Host "Launching GA Battery Management System GUI..." -ForegroundColor Green
Write-Host "Using Python: $pythonExe" -ForegroundColor Cyan
Write-Host ""

try {
    & $pythonExe -m src.app
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Application closed successfully." -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "Application exited with error code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host ""
    Write-Host "ERROR: Failed to launch application!" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Make sure Python is installed and accessible"
    Write-Host "2. Check that the virtual environment is properly set up"
    Write-Host "3. Verify that required packages (PyQt6, pyqtgraph, etc.) are installed"
}

Write-Host ""
Read-Host "Press Enter to exit"
