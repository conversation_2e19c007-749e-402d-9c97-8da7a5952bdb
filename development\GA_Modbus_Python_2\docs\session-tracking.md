# Session Tracking Feature Specification

## Overview

The Session Tracking feature provides automatic monitoring session management for the GA Modbus Python Application, enabling users to track, organize, and analyze battery monitoring sessions over time.

## Feature Scope

### Primary Goals
1. **Track monitoring sessions** - Record when battery monitoring starts/stops
2. **Associate data files** - Link CSV files to specific monitoring sessions  
3. **Session analytics** - Provide insights on monitoring duration, data quality, and battery health trends
4. **Historical tracking** - Maintain persistent history of all monitoring sessions

### User Benefits
- **Data Organization**: Easy identification of which CSV files belong to which monitoring period
- **Trend Analysis**: Compare battery performance across different monitoring sessions
- **Quality Assurance**: Track data collection completeness and identify monitoring gaps
- **Maintenance Planning**: Historical view of battery monitoring frequency and duration

### In Scope
- Session metadata tracking (start/stop times, duration)
- CSV file association with sessions
- Basic session statistics (record count, monitoring duration)
- Session history browser in GUI
- Session notes/comments functionality
- Export session history to CSV/JSON

### Out of Scope (Future Enhancements)
- Cross-session battery health analysis
- Advanced session comparison tools
- Cloud synchronization of session data
- Automated session scheduling

## Technical Architecture

### Session Data Model

```python
Session = {
    'session_id': str,           # UUID for unique identification
    'start_time': datetime,      # When monitoring started
    'end_time': datetime,        # When monitoring stopped (None if active)
    'duration_seconds': int,     # Total monitoring duration
    'status': str,              # 'active', 'completed', 'interrupted'
    'csv_files': List[str],     # Associated data files
    'connection_params': {      # Modbus connection settings used
        'port': str,
        'baudrate': int, 
        'slave_id': int
    },
    'data_summary': {           # Session statistics
        'total_records': int,
        'data_start_time': datetime,
        'data_end_time': datetime,
        'avg_cell_voltage': float,
        'avg_pack_voltage': float,
        'monitoring_gaps': int   # Times connection was lost
    },
    'notes': str,               # User-added session notes
    'created_by': str           # CLI vs GUI identifier
}
```

### Storage Strategy
- **Primary Storage**: Extend existing QSettings with JSON session data
- **Backup Storage**: Optional JSON file export for portability
- **Integration**: Leverage existing `BMSMainWindow.settings` infrastructure

### Session Manager Class Design

```python
class SessionManager:
    def __init__(self, settings: QSettings):
        self.settings = settings
        self.current_session = None
        
    def start_session(self, connection_params: dict) -> str:
        """Create new monitoring session"""
        
    def end_session(self, session_id: str) -> dict:
        """Finalize current session"""
        
    def get_session_history(self) -> List[dict]:
        """Retrieve all past sessions"""
        
    def associate_csv_file(self, session_id: str, csv_path: str):
        """Link CSV file to session"""
        
    def update_session_stats(self, session_id: str, stats: dict):
        """Update session statistics"""
```

### Integration Points

#### BMSMainWindow Integration (src/app.py)
- **Line 1535**: `start_monitoring()` - Create new session
- **Line 1566**: `stop_monitoring()` - End current session
- **Line 1581**: `handle_new_data()` - Update session statistics
- **Line 1627**: `closeEvent()` - Handle session cleanup

#### ModbusWorker Integration (src/core/modbus_worker.py)
- Session status updates on connection events
- Data quality tracking for session statistics
- Connection loss detection for gap analysis

#### CSV Writer Integration (src/ga_modbus_csv_writer.py)
- Automatic file association with active session
- Session metadata header in CSV files

## GUI Integration Plan

### New Session Management Tab
- **Session History Table**: List of all previous sessions with key metrics
- **Active Session Panel**: Current session status and real-time statistics  
- **Session Details View**: Detailed information for selected session
- **Session Actions**: Start new session, add notes, export data

### Existing Tab Enhancements
- **Real-time View**: Display current session ID and duration
- **CSV Viewer**: Filter files by session, show session association
- **Settings**: Session preferences (auto-notes, retention policy)

## Implementation Phases

### Phase 1: Core Session Tracking
- Create `SessionManager` class for session lifecycle management
- Integrate session start/stop with existing monitoring controls
- Implement basic session metadata storage using QSettings

### Phase 2: Data Association
- Link CSV files to sessions automatically
- Add session statistics calculation
- Create session history viewer

### Phase 3: Advanced Features
- Session notes and user annotations
- Session data export capabilities
- Enhanced session analytics

## Key Design Decisions

### 1. Minimal Disruption Approach
- Extend existing `BMSMainWindow` rather than major refactoring
- Leverage current QSettings infrastructure for persistence
- Maintain backward compatibility with existing CSV files

### 2. Session Boundaries
- **Session Start**: When "Start Monitoring" clicked in GUI or CLI launched
- **Session Stop**: When "Stop Monitoring" clicked, app closed, or connection lost
- **Auto-Resume**: Handle application restarts gracefully

### 3. Data Storage Strategy
- **QSettings Integration**: Store session metadata in existing settings system
- **JSON Format**: Use structured JSON for session data within QSettings
- **File Association**: Track CSV files by filename pattern and timestamp correlation

### 4. User Experience Focus
- **Non-Intrusive**: Session tracking happens automatically in background
- **Optional Interaction**: Users can add notes but don't need to manage sessions manually
- **Historical View**: Easy access to past monitoring sessions and associated data

## Session History Storage Schema

### QSettings Keys Structure
```
sessions/
     current_session_id          # Active session UUID (if any)
     session_count              # Total number of sessions
     sessions/
         {session_id}/
             metadata           # Session basic info (JSON)
             connection_params  # Modbus settings (JSON)
             data_summary      # Statistics (JSON)
             csv_files         # Associated files (JSON array)
             notes            # User notes (string)
```

### Session Status Values
- `active`: Currently monitoring
- `completed`: Normal session end
- `interrupted`: Unexpected termination
- `error`: Session failed to start

## Auto-Start Logging Integration

### Overview
Auto-start logging complements session tracking by automatically initiating CSV data logging based on current flow thresholds, enabling hands-free monitoring during charging/discharging events.

### Integration with Session Tracking

#### Session Trigger Events
- **Manual Start**: User clicks "Start Monitoring" → Creates new session immediately
- **Auto-Start**: Current threshold exceeded → Creates new session automatically
- **Hybrid Mode**: Session created manually, auto-logging activated within existing session

#### Auto-Start Configuration
```python
auto_start_settings = {
    'auto_log_mode': str,                    # 'OFF', 'ENABLE'
    'auto_log_charging_threshold': float,    # Current >= threshold starts logging (A)
    'auto_log_discharging_threshold': float, # Current <= threshold starts logging (A)
    'auto_log_charge_enable': bool,          # Enable auto-start on charging
    'auto_log_discharge_enable': bool,       # Enable auto-start on discharging
    'auto_log_charge_end_time': int,         # Minutes to continue after threshold
    'auto_log_discharge_end_time': int,      # Minutes to continue after threshold
    'pack_recovery_logging': bool            # Continue logging for OCV recovery
}
```

#### Session Management Integration
- **Auto-Session Creation**: When current thresholds are met, automatically create new session
- **Session Status Updates**: Mark session as `auto_started` vs `manual_started`
- **Auto-End Logic**: Sessions can auto-terminate after configurable time periods
- **Recovery Logging**: Extended logging for pack voltage recovery after discharge events

#### Enhanced Session Data Model
```python
# Additional fields for auto-start integration
Session = {
    # ... existing fields ...
    'trigger_type': str,        # 'manual', 'auto_charge', 'auto_discharge'
    'auto_start_threshold': float, # Threshold that triggered session
    'auto_end_enabled': bool,   # Whether session will auto-terminate
    'recovery_phase': bool,     # Tracking pack recovery after main event
    'charge_discharge_events': List[{
        'event_type': str,      # 'charge_start', 'discharge_start', 'event_end'
        'timestamp': datetime,
        'current_value': float,
        'trigger_threshold': float
    }]
}
```

### GUI Integration
- **Settings Tab**: Auto-start threshold configuration alongside session preferences
- **Real-time View**: Visual indicators for auto-start mode and current thresholds
- **Session Tab**: Filter by trigger type (manual vs auto-started sessions)

### Implementation Considerations
- **Current Validation**: Leverage existing current reading validation (false reading filtering)
- **Threshold Monitoring**: Integrate with `ModbusWorker.handle_new_data()` for real-time threshold checking
- **Session Lifecycle**: Auto-created sessions follow same data model and storage as manual sessions
- **Edge Case Handling**: Pack recovery logging extends session duration beyond main charge/discharge event

## Future Enhancement Opportunities

### Advanced Analytics
- Battery health trending across sessions
- Optimal monitoring duration recommendations
- Predictive maintenance alerts based on session patterns
- Auto-start threshold optimization based on historical patterns

### Data Management
- Session archiving and cleanup policies
- Cloud backup integration
- Cross-device session synchronization

### User Experience
- Session templates for repeated monitoring scenarios
- Automated session reporting
- Integration with external monitoring systems
- Smart threshold recommendations based on battery characteristics