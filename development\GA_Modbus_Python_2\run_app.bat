@echo off
echo Starting GA Battery Management System...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if src/app.py exists
if not exist "src\app.py" (
    echo ERROR: src\app.py not found!
    pause
    exit /b 1
)

REM Use the full path to Python
set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe

echo Using Python: %PYTHON_EXE%
echo.

REM First, let's install the required packages if they're not installed
echo Checking/installing required packages...
%PYTHON_EXE% -m pip install PyQt6 pyqtgraph pyserial pymodbus --user --quiet

echo.
echo Launching GA Battery Management System GUI...
echo.

REM Launch the application
%PYTHON_EXE% -m src.app

REM Check if the app ran successfully
if errorlevel 1 (
    echo.
    echo ERROR: Application failed to start!
    echo Check the error messages above.
    pause
) else (
    echo.
    echo Application closed successfully.
)

echo.
pause
