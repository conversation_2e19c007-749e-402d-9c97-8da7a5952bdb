Create a Python application using PyQt6 for the GUI and PyQtGraph for real-time visualization to interface with a Battery Management System (BMS) via RS232 serial communication using the Modbus RTU protocol. The application should:

Establish a serial connection to the BMS using pyserial and communicate via pymodbus to read cell voltages, current, and pack voltage every second.

Display a main window with three real-time line graphs (cell voltages, current, pack voltage) that update continuously using PyQtGraph, with time on the x-axis and appropriate units on the y-axis.

Include a table widget showing the latest numerical values for each parameter.

Log all data (timestamp, cell voltages, current, pack voltage) to a CSV file in real time using Python's logging module or csv writer.

Provide start/stop buttons to control data acquisition and a status label indicating connection state.

Handle serial connection errors gracefully with user-friendly error messages. Generate the complete Python code, including necessary imports, a modular structure with functions for serial communication, data plotting, and logging, and comments explaining key sections. Ensure the code is cross-platform compatible and optimized for real-time performance. Provide a brief explanation of how to install dependencies (pyserial, pymodbus, PyQt6, pyqtgraph) and run the application.

the main app should be called app.py

Reference @battery_pack_spec_json.json  @modbus_query_test.py